# Module 21: Observability

## Learning Objectives
- Implement metrics collection
- Add distributed tracing
- Create logging system
- Add health checks
- Implement monitoring

## Prerequisites
- Completion of Module 20: Circuit Breaking
- Understanding of observability patterns
- Knowledge of distributed systems
- Familiarity with metrics systems

## Navigation
- [Previous: Circuit Breaking](20-circuit-breaking.md)
- [Next: Security](22-security.md)

---

## Metrics Collection

Implement metrics collection using `metrics` crate:

```rust
use metrics::{
    counter, gauge, histogram,
    Key, KeyName, Label, Unit,
};
use metrics_exporter_prometheus::PrometheusBuilder;
use std::time::Instant;

pub struct LoadBalancerMetrics {
    request_count: KeyName,
    active_connections: KeyName,
    request_duration: KeyName,
    upstream_latency: KeyName,
    error_count: KeyName,
}

impl LoadBalancerMetrics {
    pub fn new() -> Self {
        // Initialize Prometheus exporter
        PrometheusBuilder::new()
            .with_http_listener(([0, 0, 0, 0], 9000))
            .install()
            .expect("failed to install Prometheus recorder");
            
        Self {
            request_count: KeyName::from("loadbalancer_requests_total"),
            active_connections: KeyName::from(
                "loadbalancer_active_connections",
            ),
            request_duration: KeyName::from(
                "loadbalancer_request_duration_seconds",
            ),
            upstream_latency: KeyName::from(
                "loadbalancer_upstream_latency_seconds",
            ),
            error_count: KeyName::from(
                "loadbalancer_errors_total",
            ),
        }
    }

    pub fn record_request(
        &self,
        method: &str,
        path: &str,
        status: u16,
    ) {
        let labels = vec![
            Label::new("method", method.to_string()),
            Label::new("path", path.to_string()),
            Label::new("status", status.to_string()),
        ];
        
        counter!(
            self.request_count,
            1,
            &labels,
        );
    }

    pub fn track_connection(&self, delta: i64) {
        gauge!(
            self.active_connections,
            delta as f64,
        );
    }

    pub fn record_request_duration(
        &self,
        start: Instant,
        method: &str,
        path: &str,
    ) {
        let duration = start.elapsed().as_secs_f64();
        let labels = vec![
            Label::new("method", method.to_string()),
            Label::new("path", path.to_string()),
        ];
        
        histogram!(
            self.request_duration,
            duration,
            &labels,
            Unit::Seconds,
        );
    }

    pub fn record_upstream_latency(
        &self,
        backend: &str,
        latency: f64,
    ) {
        let labels = vec![
            Label::new("backend", backend.to_string()),
        ];
        
        histogram!(
            self.upstream_latency,
            latency,
            &labels,
            Unit::Seconds,
        );
    }

    pub fn record_error(
        &self,
        error_type: &str,
        backend: Option<&str>,
    ) {
        let mut labels = vec![
            Label::new("type", error_type.to_string()),
        ];
        
        if let Some(backend) = backend {
            labels.push(Label::new("backend", backend.to_string()));
        }
        
        counter!(
            self.error_count,
            1,
            &labels,
        );
    }
}
```

---

## Distributed Tracing

Add distributed tracing with OpenTelemetry:

```rust
use opentelemetry::{
    global,
    trace::{TraceContextExt, Tracer},
    Context, KeyValue,
};
use opentelemetry_jaeger::new_pipeline;
use std::time::Duration;

pub struct LoadBalancerTracing {
    tracer: opentelemetry::sdk::trace::Tracer,
}

impl LoadBalancerTracing {
    pub fn new(service_name: &str) -> Self {
        let tracer = new_pipeline()
            .with_service_name(service_name)
            .with_max_packet_size(65000)
            .with_agent_endpoint("127.0.0.1:6831")
            .install_simple()
            .expect("failed to install Jaeger tracer");
            
        Self { tracer }
    }

    pub fn start_request_span(
        &self,
        method: &str,
        path: &str,
    ) -> Context {
        let mut span = self.tracer
            .span_builder("http_request")
            .with_attributes(vec![
                KeyValue::new("http.method", method.to_string()),
                KeyValue::new("http.path", path.to_string()),
            ])
            .start(&self.tracer);
            
        Context::current_with_span(span)
    }

    pub fn start_backend_span(
        &self,
        ctx: &Context,
        backend: &str,
    ) -> Context {
        let parent_span = ctx.span();
        let mut span = self.tracer
            .span_builder("backend_request")
            .with_parent(parent_span)
            .with_attributes(vec![
                KeyValue::new("backend", backend.to_string()),
            ])
            .start(&self.tracer);
            
        Context::current_with_span(span)
    }

    pub fn record_error(
        span: &opentelemetry::trace::Span,
        error: &str,
    ) {
        span.set_attribute(KeyValue::new("error", true));
        span.set_attribute(KeyValue::new(
            "error.message",
            error.to_string(),
        ));
    }
}
```

---

## Logging System

Implement structured logging with `tracing`:

```rust
use tracing::{
    field::Field,
    Level, Subscriber,
    subscriber::set_global_default,
};
use tracing_subscriber::{
    fmt::format::FmtSpan,
    layer::SubscriberExt,
    EnvFilter,
};
use tracing_bunyan_formatter::{
    BunyanFormattingLayer, JsonStorageLayer,
};
use tracing_log::LogTracer;

pub struct LoadBalancerLogging {
    app_name: String,
}

impl LoadBalancerLogging {
    pub fn new(app_name: &str) -> Self {
        Self {
            app_name: app_name.to_string(),
        }
    }

    pub fn init(&self) {
        // Initialize LogTracer
        LogTracer::init()
            .expect("failed to initialize log tracer");
            
        // Create Bunyan formatting layer
        let formatting_layer = BunyanFormattingLayer::new(
            self.app_name.clone(),
            std::io::stdout,
        );
        
        // Create subscriber with multiple layers
        let subscriber = tracing_subscriber::registry()
            .with(JsonStorageLayer::new())
            .with(formatting_layer)
            .with(EnvFilter::from_default_env())
            .with(tracing_opentelemetry::layer().with_tracer(
                opentelemetry::global::tracer("loadbalancer"),
            ));
            
        // Set global subscriber
        set_global_default(subscriber)
            .expect("failed to set tracing subscriber");
    }

    pub fn log_request(
        &self,
        method: &str,
        path: &str,
        status: u16,
        duration: f64,
    ) {
        tracing::info!(
            method = %method,
            path = %path,
            status = %status,
            duration_ms = %duration,
            "http_request",
        );
    }

    pub fn log_backend_request(
        &self,
        backend: &str,
        status: u16,
        latency: f64,
    ) {
        tracing::info!(
            backend = %backend,
            status = %status,
            latency_ms = %latency,
            "backend_request",
        );
    }

    pub fn log_error(
        &self,
        error: &str,
        context: &str,
    ) {
        tracing::error!(
            error = %error,
            context = %context,
            "error_occurred",
        );
    }
}
```

---

## Health Checks

Implement health checking system:

```rust
use tokio::time::{interval, Duration};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;

#[derive(Debug, Clone)]
pub struct HealthStatus {
    pub healthy: bool,
    pub last_check: std::time::Instant,
    pub last_error: Option<String>,
}

pub struct HealthChecker {
    backends: Arc<RwLock<HashMap<String, Backend>>>,
    check_interval: Duration,
    timeout: Duration,
    statuses: Arc<RwLock<HashMap<String, HealthStatus>>>,
}

impl HealthChecker {
    pub fn new(
        backends: Arc<RwLock<HashMap<String, Backend>>>,
        check_interval: Duration,
        timeout: Duration,
    ) -> Self {
        Self {
            backends,
            check_interval,
            timeout,
            statuses: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    pub async fn start(&self) {
        let mut interval = interval(self.check_interval);
        
        loop {
            interval.tick().await;
            self.check_all_backends().await;
        }
    }

    async fn check_all_backends(&self) {
        let backends = self.backends.read().await;
        
        for (name, backend) in backends.iter() {
            self.check_backend(name, backend).await;
        }
    }

    async fn check_backend(
        &self,
        name: &str,
        backend: &Backend,
    ) {
        let client = reqwest::Client::new();
        let url = format!("{}/health", backend.url);
        
        let result = tokio::time::timeout(
            self.timeout,
            client.get(&url).send(),
        )
        .await;
        
        let status = match result {
            Ok(Ok(response)) if response.status().is_success() => {
                HealthStatus {
                    healthy: true,
                    last_check: std::time::Instant::now(),
                    last_error: None,
                }
            }
            Ok(Ok(response)) => HealthStatus {
                healthy: false,
                last_check: std::time::Instant::now(),
                last_error: Some(format!(
                    "Unhealthy status: {}",
                    response.status(),
                )),
            },
            Ok(Err(e)) => HealthStatus {
                healthy: false,
                last_check: std::time::Instant::now(),
                last_error: Some(format!(
                    "Request error: {}",
                    e,
                )),
            },
            Err(_) => HealthStatus {
                healthy: false,
                last_check: std::time::Instant::now(),
                last_error: Some("Timeout".to_string()),
            },
        };
        
        self.statuses
            .write()
            .await
            .insert(name.to_string(), status);
    }

    pub async fn is_backend_healthy(
        &self,
        name: &str,
    ) -> bool {
        self.statuses
            .read()
            .await
            .get(name)
            .map(|status| status.healthy)
            .unwrap_or(false)
    }

    pub async fn get_health_report(&self) -> HashMap<String, HealthStatus> {
        self.statuses.read().await.clone()
    }
}
```

---

## Monitoring Integration

Create monitoring endpoints:

```rust
use warp::Filter;
use std::collections::HashMap;
use serde::Serialize;

#[derive(Serialize)]
struct HealthResponse {
    status: String,
    version: String,
    backends: HashMap<String, HealthStatus>,
    metrics: MetricsSnapshot,
}

#[derive(Serialize)]
struct MetricsSnapshot {
    total_requests: u64,
    active_connections: i64,
    error_rate: f64,
    average_latency: f64,
}

pub async fn start_monitoring(
    health_checker: Arc<HealthChecker>,
    metrics: Arc<LoadBalancerMetrics>,
) {
    let health_route = warp::path("health")
        .and(with_health_checker(health_checker.clone()))
        .and(with_metrics(metrics.clone()))
        .and_then(health_handler);
        
    let metrics_route = warp::path("metrics")
        .and(with_metrics(metrics))
        .and_then(metrics_handler);
        
    let routes = health_route
        .or(metrics_route)
        .with(warp::cors().allow_any_origin());
        
    warp::serve(routes)
        .run(([0, 0, 0, 0], 9001))
        .await;
}

async fn health_handler(
    health_checker: Arc<HealthChecker>,
    metrics: Arc<LoadBalancerMetrics>,
) -> Result<impl warp::Reply, warp::Rejection> {
    let backends = health_checker.get_health_report().await;
    let metrics_snapshot = metrics.get_snapshot().await;
    
    let response = HealthResponse {
        status: "ok".to_string(),
        version: env!("CARGO_PKG_VERSION").to_string(),
        backends,
        metrics: metrics_snapshot,
    };
    
    Ok(warp::reply::json(&response))
}

async fn metrics_handler(
    metrics: Arc<LoadBalancerMetrics>,
) -> Result<impl warp::Reply, warp::Rejection> {
    let snapshot = metrics.get_snapshot().await;
    Ok(warp::reply::json(&snapshot))
}

fn with_health_checker(
    health_checker: Arc<HealthChecker>,
) -> impl Filter<Extract = (Arc<HealthChecker>,), Error = std::convert::Infallible> + Clone {
    warp::any().map(move || health_checker.clone())
}

fn with_metrics(
    metrics: Arc<LoadBalancerMetrics>,
) -> impl Filter<Extract = (Arc<LoadBalancerMetrics>,), Error = std::convert::Infallible> + Clone {
    warp::any().map(move || metrics.clone())
}
```

---

## Summary
- Implemented metrics collection
- Added distributed tracing
- Created logging system
- Added health checks
- Implemented monitoring endpoints

---

## Next Steps
Continue to [Module 22: Security](22-security.md) to learn about implementing security features.
