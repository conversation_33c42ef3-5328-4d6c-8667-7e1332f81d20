# Module 09: Advanced Load Balancing

## Learning Objectives
- Implement weighted round-robin balancing
- Add least connections load balancing
- Create IP hash-based sticky sessions
- Implement consistent hashing for backend selection

## Prerequisites
- Completion of Module 08: TLS and Security
- Understanding of hashing algorithms
- Familiarity with session persistence concepts
- Knowledge of advanced load balancing algorithms

## Navigation
- [Previous: TLS and Security](08-tls-security.md)
- [Next: High Availability](10-high-availability.md)

---

## Weighted Round-Robin

Enhance the basic round-robin algorithm to support backend weights:

```rust
use std::sync::{Arc, Mutex};
use std::collections::VecDeque;

pub struct WeightedRoundRobin {
    backends: Vec<Backend>,
    current_weights: Arc<Mutex<VecDeque<(usize, u32)>>>,
}

struct Backend {
    addr: SocketAddr,
    weight: u32,
}

impl WeightedRoundRobin {
    pub fn new(backends: Vec<(SocketAddr, u32)>) -> Self {
        let backends = backends
            .into_iter()
            .map(|(addr, weight)| Backend { addr, weight })
            .collect();
        
        let current_weights = backends
            .iter()
            .enumerate()
            .map(|(i, b)| (i, b.weight))
            .collect();
            
        Self {
            backends,
            current_weights: Arc::new(Mutex::new(current_weights)),
        }
    }

    pub fn next(&self) -> SocketAddr {
        let mut weights = self.current_weights.lock().unwrap();
        
        // Find backend with highest current weight
        let (index, _) = weights
            .iter()
            .enumerate()
            .max_by_key(|(_, &w)| w)
            .unwrap();
            
        // Decrease selected backend's weight by total weight
        let total_weight: u32 = self.backends.iter().map(|b| b.weight).sum();
        weights[index].1 -= total_weight;
        
        // Increase all weights by their backend's weight
        for (i, weight) in weights.iter_mut() {
            *weight += self.backends[i].weight;
        }
        
        self.backends[index].addr
    }
}
```

---

## Least Connections

Select the backend with the fewest active connections:

```rust
use std::sync::atomic::{AtomicUsize, Ordering};

pub struct LeastConnections {
    backends: Vec<BackendState>,
}

struct BackendState {
    addr: SocketAddr,
    connections: AtomicUsize,
}

impl LeastConnections {
    pub fn new(backends: Vec<SocketAddr>) -> Self {
        let backends = backends
            .into_iter()
            .map(|addr| BackendState {
                addr,
                connections: AtomicUsize::new(0),
            })
            .collect();
            
        Self { backends }
    }

    pub fn next(&self) -> (SocketAddr, ConnectionTracker) {
        let backend = self.backends
            .iter()
            .min_by_key(|b| b.connections.load(Ordering::Relaxed))
            .unwrap();
            
        backend.connections.fetch_add(1, Ordering::Relaxed);
        
        (backend.addr, ConnectionTracker::new(backend))
    }
}

pub struct ConnectionTracker {
    backend: *const BackendState,
}

impl ConnectionTracker {
    fn new(backend: &BackendState) -> Self {
        Self {
            backend: backend as *const _,
        }
    }
}

impl Drop for ConnectionTracker {
    fn drop(&mut self) {
        unsafe {
            (*self.backend).connections.fetch_sub(1, Ordering::Relaxed);
        }
    }
}
```

---

## IP Hash Sticky Sessions

Implement session persistence based on client IP:

```rust
use std::hash::{Hash, Hasher};
use std::collections::hash_map::DefaultHasher;

pub struct IpHash {
    backends: Vec<SocketAddr>,
}

impl IpHash {
    pub fn new(backends: Vec<SocketAddr>) -> Self {
        Self { backends }
    }

    pub fn get_backend(&self, client_ip: &IpAddr) -> SocketAddr {
        let mut hasher = DefaultHasher::new();
        client_ip.hash(&mut hasher);
        let hash = hasher.finish();
        
        let index = hash as usize % self.backends.len();
        self.backends[index]
    }
}
```

---

## Consistent Hashing

Implement consistent hashing for better distribution and minimal redistribution:

```rust
use std::collections::BTreeMap;

pub struct ConsistentHash {
    ring: BTreeMap<u64, SocketAddr>,
    replicas: usize,
}

impl ConsistentHash {
    pub fn new(backends: Vec<SocketAddr>, replicas: usize) -> Self {
        let mut ring = BTreeMap::new();
        
        for addr in backends {
            for i in 0..replicas {
                let key = format!("{}:{}", addr, i);
                let hash = Self::hash_key(&key);
                ring.insert(hash, addr);
            }
        }
        
        Self { ring, replicas }
    }

    pub fn get_backend(&self, key: &str) -> SocketAddr {
        let hash = Self::hash_key(key);
        
        match self.ring.range(hash..).next() {
            Some((_, addr)) => *addr,
            None => *self.ring.iter().next().unwrap().1,
        }
    }

    pub fn add_backend(&mut self, addr: SocketAddr) {
        for i in 0..self.replicas {
            let key = format!("{}:{}", addr, i);
            let hash = Self::hash_key(&key);
            self.ring.insert(hash, addr);
        }
    }

    pub fn remove_backend(&mut self, addr: &SocketAddr) {
        for i in 0..self.replicas {
            let key = format!("{}:{}", addr, i);
            let hash = Self::hash_key(&key);
            self.ring.remove(&hash);
        }
    }

    fn hash_key(key: &str) -> u64 {
        let mut hasher = DefaultHasher::new();
        key.hash(&mut hasher);
        hasher.finish()
    }
}
```

---

## Load Balancer Integration

Create a unified interface for different algorithms:

```rust
pub enum Algorithm {
    RoundRobin(WeightedRoundRobin),
    LeastConnections(LeastConnections),
    IpHash(IpHash),
    ConsistentHash(ConsistentHash),
}

impl Algorithm {
    pub fn select_backend(&self, context: &RequestContext) -> SocketAddr {
        match self {
            Algorithm::RoundRobin(rr) => rr.next(),
            Algorithm::LeastConnections(lc) => lc.next().0,
            Algorithm::IpHash(ih) => ih.get_backend(&context.client_ip),
            Algorithm::ConsistentHash(ch) => ch.get_backend(&context.session_id),
        }
    }
}

pub struct RequestContext {
    pub client_ip: IpAddr,
    pub session_id: String,
    pub headers: HeaderMap,
}
```

---

## Algorithm Selection Strategy

Choose the best algorithm based on your needs:

1. **Weighted Round-Robin**
   - Use when: Backends have different capacities
   - Pros: Simple, predictable
   - Cons: Doesn't account for actual load

2. **Least Connections**
   - Use when: Request processing times vary significantly
   - Pros: Better load distribution
   - Cons: More overhead, potential thundering herd

3. **IP Hash**
   - Use when: Session stickiness is required
   - Pros: Simple session persistence
   - Cons: Uneven distribution possible

4. **Consistent Hashing**
   - Use when: Backends change frequently
   - Pros: Minimal redistribution on change
   - Cons: More complex, memory usage

---

## Summary
- Implemented multiple load balancing algorithms
- Added session persistence options
- Created unified algorithm interface
- Provided selection guidance

---

## Next Steps
Continue to [Module 10: High Availability](10-high-availability.md) to learn about ensuring load balancer reliability.
