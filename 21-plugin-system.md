# 15 - Rate Limiting and DoS Protection

## Goal
Add per-client and global rate limiting to prevent abuse and denial-of-service attacks.

## Concepts Introduced
- Token bucket and leaky bucket algorithms
- Tracking client state
- Handling abuse gracefully

## Why Rate Limiting?
It protects the load balancer and backends from overload and malicious traffic.

## Alternatives Considered
- No rate limiting (risky)
- Relying on upstream firewalls (less flexible)

## Next Steps
We'll implement graceful shutdown for safe restarts and upgrades.
