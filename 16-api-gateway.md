# Module 16: API Gateway Features

## Learning Objectives
- Implement API routing and versioning
- Add request/response transformation
- Create API documentation generation
- Implement API key management
- Add request validation

## Prerequisites
- Completion of Module 15: Advanced Security
- Understanding of API gateway patterns
- Familiarity with OpenAPI specifications
- Knowledge of API security best practices

## Navigation
- [Previous: Advanced Security](15-advanced-security.md)
- [Next: Service Discovery](17-service-discovery.md)

---

## API Routing

Implement sophisticated API routing:

```rust
use regex::Regex;
use std::collections::HashMap;

pub struct ApiRouter {
    routes: Vec<Route>,
    version_extractor: VersionExtractor,
}

struct Route {
    pattern: Regex,
    backend: BackendService,
    version: ApiVersion,
    methods: Vec<Method>,
}

enum VersionExtractor {
    Header(String),
    Path,
    Query(String),
}

struct ApiVersion {
    major: u32,
    minor: u32,
}

impl ApiRouter {
    pub fn new() -> Self {
        Self {
            routes: Vec::new(),
            version_extractor: VersionExtractor::Header(
                "x-api-version".to_string(),
            ),
        }
    }

    pub async fn route_request(
        &self,
        request: &Request<Body>,
    ) -> Result<BackendService> {
        let version = self.extract_version(request)?;
        let path = request.uri().path();
        let method = request.method();
        
        for route in &self.routes {
            if route.pattern.is_match(path) 
                && route.version >= version
                && route.methods.contains(method)
            {
                return Ok(route.backend.clone());
            }
        }
        
        Err(Error::NoMatchingRoute)
    }

    fn extract_version(&self, request: &Request<Body>) -> Result<ApiVersion> {
        match &self.version_extractor {
            VersionExtractor::Header(name) => {
                let header = request
                    .headers()
                    .get(name)
                    .ok_or(Error::MissingVersion)?;
                    
                Self::parse_version(header.to_str()?)
            }
            VersionExtractor::Path => {
                let path = request.uri().path();
                let captures = Regex::new(r"v(\d+)\.(\d+)")
                    .unwrap()
                    .captures(path)
                    .ok_or(Error::MissingVersion)?;
                    
                Ok(ApiVersion {
                    major: captures[1].parse()?,
                    minor: captures[2].parse()?,
                })
            }
            VersionExtractor::Query(param) => {
                let query = request.uri().query()
                    .ok_or(Error::MissingVersion)?;
                    
                let version = url::form_urlencoded::parse(query.as_bytes())
                    .find(|(k, _)| k == param)
                    .ok_or(Error::MissingVersion)?
                    .1;
                    
                Self::parse_version(&version)
            }
        }
    }
}
```

---

## Request Transformation

Implement request transformation middleware:

```rust
use serde_json::Value;
use jsonpath_lib as jsonpath;

pub struct RequestTransformer {
    transformations: Vec<Transformation>,
}

enum Transformation {
    AddHeader {
        name: String,
        value: String,
    },
    RemoveHeader {
        name: String,
    },
    ModifyPath {
        pattern: Regex,
        replacement: String,
    },
    TransformBody {
        jsonpath: String,
        operation: JsonOperation,
    },
}

enum JsonOperation {
    Set(Value),
    Remove,
    Rename(String),
    Transform(Box<dyn Fn(Value) -> Value + Send + Sync>),
}

impl RequestTransformer {
    pub async fn transform_request(
        &self,
        mut request: Request<Body>,
    ) -> Result<Request<Body>> {
        // Apply header transformations
        for transform in &self.transformations {
            match transform {
                Transformation::AddHeader { name, value } => {
                    request.headers_mut().insert(
                        name.parse()?,
                        value.parse()?,
                    );
                }
                Transformation::RemoveHeader { name } => {
                    request.headers_mut().remove(name);
                }
                Transformation::ModifyPath { pattern, replacement } => {
                    let uri = request.uri().to_string();
                    let new_uri = pattern.replace_all(&uri, replacement);
                    *request.uri_mut() = new_uri.parse()?;
                }
                Transformation::TransformBody { jsonpath, operation } => {
                    let body = hyper::body::to_bytes(request.body_mut()).await?;
                    let mut json: Value = serde_json::from_slice(&body)?;
                    
                    if let Some(value) = jsonpath::select(&json, jsonpath)? {
                        match operation {
                            JsonOperation::Set(new_value) => {
                                *value = new_value.clone();
                            }
                            JsonOperation::Remove => {
                                *value = Value::Null;
                            }
                            JsonOperation::Rename(new_name) => {
                                if let Value::Object(map) = value {
                                    if let Some(old_value) = map.remove(jsonpath) {
                                        map.insert(new_name.clone(), old_value);
                                    }
                                }
                            }
                            JsonOperation::Transform(f) => {
                                *value = f(value.clone());
                            }
                        }
                    }
                    
                    *request.body_mut() = Body::from(
                        serde_json::to_vec(&json)?,
                    );
                }
            }
        }
        
        Ok(request)
    }
}
```

---

## API Documentation

Generate OpenAPI documentation:

```rust
use openapiv3::*;
use std::collections::BTreeMap;

pub struct ApiDocGenerator {
    spec: OpenAPI,
}

impl ApiDocGenerator {
    pub fn new(info: Info) -> Self {
        let mut spec = OpenAPI {
            openapi: "3.0.3".to_string(),
            info,
            ..Default::default()
        };
        
        Self { spec }
    }

    pub fn add_path(
        &mut self,
        path: &str,
        methods: Vec<(Method, Operation)>,
    ) {
        let item = PathItem {
            get: methods.iter()
                .find(|(m, _)| m == &Method::GET)
                .map(|(_, op)| op.clone()),
            post: methods.iter()
                .find(|(m, _)| m == &Method::POST)
                .map(|(_, op)| op.clone()),
            put: methods.iter()
                .find(|(m, _)| m == &Method::PUT)
                .map(|(_, op)| op.clone()),
            delete: methods.iter()
                .find(|(m, _)| m == &Method::DELETE)
                .map(|(_, op)| op.clone()),
            ..Default::default()
        };
        
        self.spec.paths.insert(path.to_string(), item);
    }

    pub fn add_schema<T: serde::Serialize>(
        &mut self,
        name: &str,
    ) -> Result<(), Error> {
        let schema = schemars::schema_for!(T);
        self.spec.components.schemas.insert(
            name.to_string(),
            schema.into(),
        );
        Ok(())
    }

    pub fn generate(&self) -> Result<String, Error> {
        serde_json::to_string_pretty(&self.spec)
            .map_err(Error::from)
    }
}
```

---

## API Key Management

Implement API key authentication:

```rust
use tokio::sync::RwLock;
use std::collections::HashMap;
use blake3::Hash;

pub struct ApiKeyManager {
    keys: RwLock<HashMap<Hash, ApiKey>>,
}

struct ApiKey {
    id: String,
    hashed_key: Hash,
    scopes: Vec<String>,
    rate_limit: Option<RateLimit>,
    metadata: HashMap<String, String>,
}

impl ApiKeyManager {
    pub fn new() -> Self {
        Self {
            keys: RwLock::new(HashMap::new()),
        }
    }

    pub async fn create_key(
        &self,
        scopes: Vec<String>,
        rate_limit: Option<RateLimit>,
        metadata: HashMap<String, String>,
    ) -> Result<(String, String)> {
        // Generate random API key
        let key = uuid::Uuid::new_v4().to_string();
        let id = uuid::Uuid::new_v4().to_string();
        
        // Hash the key
        let hashed_key = blake3::hash(key.as_bytes());
        
        let api_key = ApiKey {
            id: id.clone(),
            hashed_key,
            scopes,
            rate_limit,
            metadata,
        };
        
        // Store the key
        self.keys.write().await.insert(hashed_key, api_key);
        
        Ok((id, key))
    }

    pub async fn validate_key(
        &self,
        key: &str,
        scope: &str,
    ) -> Result<bool> {
        let hashed_key = blake3::hash(key.as_bytes());
        
        if let Some(api_key) = self.keys.read().await.get(&hashed_key) {
            // Check if key has required scope
            if !api_key.scopes.contains(&scope.to_string()) {
                return Ok(false);
            }
            
            // Check rate limit if any
            if let Some(rate_limit) = &api_key.rate_limit {
                if !rate_limit.check().await {
                    return Ok(false);
                }
            }
            
            Ok(true)
        } else {
            Ok(false)
        }
    }

    pub async fn revoke_key(&self, id: &str) -> Result<()> {
        let mut keys = self.keys.write().await;
        
        if let Some(key) = keys
            .iter()
            .find(|(_, k)| k.id == id)
            .map(|(h, _)| *h)
        {
            keys.remove(&key);
        }
        
        Ok(())
    }
}
```

---

## Request Validation

Implement request validation:

```rust
use jsonschema::{JSONSchema, Draft};
use serde_json::Value;

pub struct RequestValidator {
    schemas: HashMap<String, JSONSchema>,
}

impl RequestValidator {
    pub fn new() -> Self {
        Self {
            schemas: HashMap::new(),
        }
    }

    pub fn add_schema(
        &mut self,
        name: &str,
        schema: Value,
    ) -> Result<(), Error> {
        let compiled = JSONSchema::options()
            .with_draft(Draft::Draft7)
            .compile(&schema)?;
            
        self.schemas.insert(name.to_string(), compiled);
        Ok(())
    }

    pub async fn validate_request(
        &self,
        request: &Request<Body>,
        schema_name: &str,
    ) -> Result<(), ValidationError> {
        let schema = self.schemas
            .get(schema_name)
            .ok_or(ValidationError::SchemaNotFound)?;
            
        // Validate headers
        let headers: Value = serde_json::to_value(
            request.headers()
                .iter()
                .map(|(k, v)| (k.as_str(), v.to_str().unwrap()))
                .collect::<HashMap<_, _>>()
        )?;
        
        if let Err(errors) = schema.validate(&headers) {
            return Err(ValidationError::InvalidHeaders(errors));
        }
        
        // Validate query parameters
        if let Some(query) = request.uri().query() {
            let params: Value = serde_json::to_value(
                url::form_urlencoded::parse(query.as_bytes())
                    .collect::<HashMap<_, _>>()
            )?;
            
            if let Err(errors) = schema.validate(&params) {
                return Err(ValidationError::InvalidQuery(errors));
            }
        }
        
        // Validate body
        let body_bytes = hyper::body::to_bytes(request.body()).await?;
        if !body_bytes.is_empty() {
            let body: Value = serde_json::from_slice(&body_bytes)?;
            
            if let Err(errors) = schema.validate(&body) {
                return Err(ValidationError::InvalidBody(errors));
            }
        }
        
        Ok(())
    }
}
```

---

## Summary
- Implemented API routing and versioning
- Added request transformation
- Created API documentation generation
- Implemented API key management
- Added request validation

---

## Next Steps
Continue to [Module 17: Service Discovery](17-service-discovery.md) to learn about dynamic backend discovery and registration.
