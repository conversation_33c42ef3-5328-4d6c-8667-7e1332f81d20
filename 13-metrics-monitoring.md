
# Module 07: Health Checks

## Learning Objectives
- Understand the importance of health checks in load balancing
- Implement periodic health checks for backend servers
- Integrate health status with the backend pool and load balancer
- Handle backend failures and automatic recovery
- Explore advanced health check strategies

## Why Health Checks?
Health checks ensure that only healthy backend servers receive traffic, improving reliability and user experience. They enable automatic failover and recovery, making the load balancer resilient to server failures.

## Health Check Strategies

**Active Health Checks:**
- Periodically send requests (HTTP or TCP) to backends
- Mark servers as unhealthy if they fail checks

**Passive Health Checks:**
- Monitor real traffic for failures
- Mark servers as unhealthy based on observed errors

**Hybrid:**
- Combine both for fast and accurate detection

## Implementation: Periodic HTTP Health Checks

We'll implement active HTTP health checks using Tokio's async tasks and intervals.

### Step 1: Health Check Configuration

```rust
// src/health/config.rs
use serde::{Deserialize, Serialize};
use std::time::Duration;

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct HealthCheckConfig {
	pub interval: Duration,
	pub timeout: Duration,
	pub path: String,
	pub expected_status: Vec<u16>,
}

impl Default for HealthCheckConfig {
	fn default() -> Self {
		Self {
			interval: Duration::from_secs(10),
			timeout: Duration::from_secs(2),
			path: "/health".to_string(),
			expected_status: vec![200],
		}
	}
}
```

### Step 2: Health Checker Implementation

```rust
// src/health/checker.rs
use crate::backend::{BackendPool, BackendStatus};
use crate::health::HealthCheckConfig;
use hyper::{Client, Uri};
use std::net::SocketAddr;
use std::sync::Arc;
use tokio::time::{interval, timeout};
use tracing::{info, warn, error};

pub struct HealthChecker {
	config: HealthCheckConfig,
	backend_pool: Arc<BackendPool>,
	client: Client<hyper::client::HttpConnector>,
}

impl HealthChecker {
	pub fn new(config: HealthCheckConfig, backend_pool: Arc<BackendPool>) -> Self {
		Self {
			config,
			backend_pool,
			client: Client::new(),
		}
	}

	pub async fn start(&self) {
		let mut interval = interval(self.config.interval);
		loop {
			interval.tick().await;
			let backends = self.backend_pool.get_all_backends();
			for backend in backends {
				let client = self.client.clone();
				let config = self.config.clone();
				let pool = Arc::clone(&self.backend_pool);
				tokio::spawn(async move {
					let uri: Uri = format!("http://{}{}", backend.addr, config.path).parse().unwrap();
					let result = timeout(config.timeout, client.get(uri)).await;
					let healthy = match result {
						Ok(Ok(resp)) => config.expected_status.contains(&resp.status().as_u16()),
						_ => false,
					};
					pool.update_backend_status(
						backend.addr,
						if healthy { BackendStatus::Healthy } else { BackendStatus::Unhealthy },
					);
					if healthy {
						info!("Backend {} is healthy", backend.addr);
					} else {
						warn!("Backend {} is unhealthy", backend.addr);
					}
				});
			}
		}
	}
}
```

### Step 3: Integrate with Load Balancer

Update your main application to spawn the health checker:

```rust
// src/main.rs
use rusty_balancer::{backend::BackendPool, health::{HealthChecker, HealthCheckConfig}};
use std::sync::Arc;

#[tokio::main]
async fn main() -> rusty_balancer::Result<()> {
	let backend_pool = Arc::new(BackendPool::new());
	// ... add backends ...
	let health_checker = HealthChecker::new(HealthCheckConfig::default(), Arc::clone(&backend_pool));
	tokio::spawn(async move {
		health_checker.start().await;
	});
	// ... start proxy ...
	Ok(())
}
```

### Step 4: Testing and Observability

1. Start your backend servers and the load balancer.
2. Stop one backend and observe logs showing it marked unhealthy.
3. Restart the backend and see it marked healthy again.

### Advanced Topics

- **TCP health checks:** Use `tokio::net::TcpStream::connect` for non-HTTP services.
- **Custom health logic:** Extend the checker for application-specific checks.
- **Exponential backoff:** Increase interval after repeated failures.
- **Jitter:** Add randomness to intervals to avoid thundering herd.
- **Metrics:** Track health check results for monitoring.

## Key Takeaways
- Health checks are essential for high availability and reliability.
- Active checks proactively detect failures; passive checks observe real traffic.
- Integrating health status with the backend pool enables automatic failover.
- Advanced strategies (backoff, jitter, custom logic) improve resilience.

## Navigation
- [Previous: Configuration System](06-configuration-system.md)
- [Next: HTTP Load Balancing](08-http-load-balancing.md)
