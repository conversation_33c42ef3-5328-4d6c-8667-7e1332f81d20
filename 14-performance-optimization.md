# Module 14: Performance Optimization

## Learning Objectives
- Implement connection pooling
- Add request pipelining
- Optimize memory usage
- Implement caching strategies
- Profile and optimize performance bottlenecks

## Prerequisites
- Completion of Module 13: Observability
- Understanding of performance optimization techniques
- Familiarity with connection pooling concepts
- Knowledge of profiling tools

## Navigation
- [Previous: Observability](13-observability.md)
- [Next: Advanced Security](15-advanced-security.md)

---

## Connection Pooling

Implement an efficient connection pool:

```rust
use tokio::net::TcpStream;
use tokio::sync::{Mutex, Semaphore};
use std::collections::VecDeque;
use std::time::{Duration, Instant};

pub struct ConnectionPool {
    connections: Mutex<VecDeque<PooledConnection>>,
    capacity: usize,
    semaphore: Semaphore,
    timeout: Duration,
    backend: SocketAddr,
}

struct PooledConnection {
    stream: TcpStream,
    last_used: Instant,
}

impl ConnectionPool {
    pub fn new(backend: SocketAddr, capacity: usize, timeout: Duration) -> Self {
        Self {
            connections: Mutex::new(VecDeque::with_capacity(capacity)),
            capacity,
            semaphore: Semaphore::new(capacity),
            timeout,
            backend,
        }
    }

    pub async fn get_connection(&self) -> Result<PoolConnection<'_>, Error> {
        let _permit = self.semaphore.acquire().await?;
        
        let mut connections = self.connections.lock().await;
        
        // Try to reuse existing connection
        while let Some(mut conn) = connections.pop_front() {
            if conn.last_used.elapsed() > self.timeout {
                // Connection too old, drop it
                continue;
            }
            
            // Verify connection is still alive
            if Self::check_connection(&mut conn.stream).await {
                return Ok(PoolConnection {
                    pool: self,
                    connection: Some(conn),
                });
            }
        }
        
        // Create new connection
        let stream = TcpStream::connect(self.backend).await?;
        let conn = PooledConnection {
            stream,
            last_used: Instant::now(),
        };
        
        Ok(PoolConnection {
            pool: self,
            connection: Some(conn),
        })
    }

    async fn check_connection(stream: &mut TcpStream) -> bool {
        // Send TCP keep-alive probe
        if let Err(_) = stream.writable().await {
            return false;
        }
        true
    }
}

pub struct PoolConnection<'a> {
    pool: &'a ConnectionPool,
    connection: Option<PooledConnection>,
}

impl<'a> Drop for PoolConnection<'a> {
    fn drop(&mut self) {
        if let Some(mut conn) = self.connection.take() {
            conn.last_used = Instant::now();
            
            let pool = self.pool;
            tokio::spawn(async move {
                let mut connections = pool.connections.lock().await;
                connections.push_back(conn);
            });
        }
    }
}
```

---

## Request Pipelining

Implement HTTP request pipelining:

```rust
use futures::stream::{FuturesUnordered, StreamExt};
use std::sync::atomic::{AtomicUsize, Ordering};

pub struct RequestPipeline {
    max_concurrent: usize,
    active_requests: AtomicUsize,
    connection_pool: Arc<ConnectionPool>,
}

impl RequestPipeline {
    pub fn new(
        max_concurrent: usize,
        connection_pool: Arc<ConnectionPool>,
    ) -> Self {
        Self {
            max_concurrent,
            active_requests: AtomicUsize::new(0),
            connection_pool,
        }
    }

    pub async fn send_requests<I>(&self, requests: I) -> Result<Vec<Response<Body>>>
    where
        I: IntoIterator<Item = Request<Body>>,
    {
        let mut futures = FuturesUnordered::new();
        let mut responses = Vec::new();
        
        for request in requests {
            // Wait if we've reached max concurrent requests
            while self.active_requests.load(Ordering::Relaxed) >= self.max_concurrent {
                tokio::time::sleep(Duration::from_millis(1)).await;
            }
            
            self.active_requests.fetch_add(1, Ordering::Relaxed);
            
            let connection_pool = self.connection_pool.clone();
            let request_future = async move {
                let conn = connection_pool.get_connection().await?;
                let response = Self::send_request(conn, request).await?;
                Ok::<_, Error>(response)
            };
            
            futures.push(request_future);
        }
        
        while let Some(result) = futures.next().await {
            self.active_requests.fetch_sub(1, Ordering::Relaxed);
            responses.push(result?);
        }
        
        Ok(responses)
    }

    async fn send_request(
        mut conn: PoolConnection<'_>,
        request: Request<Body>,
    ) -> Result<Response<Body>, Error> {
        // Implement request sending logic
        todo!()
    }
}
```

---

## Memory Optimization

Implement memory-efficient request handling:

```rust
use bytes::{Bytes, BytesMut};
use tokio::io::{AsyncRead, AsyncWrite, BufReader};

pub struct StreamingRequestHandler<S> {
    stream: BufReader<S>,
    buffer: BytesMut,
}

impl<S: AsyncRead + AsyncWrite + Unpin> StreamingRequestHandler<S> {
    pub fn new(stream: S) -> Self {
        Self {
            stream: BufReader::new(stream),
            buffer: BytesMut::with_capacity(8 * 1024), // 8KB buffer
        }
    }

    pub async fn handle_request(&mut self) -> Result<Response<Body>, Error> {
        // Read request headers
        let headers = self.read_headers().await?;
        
        // Create streaming body
        let body = if let Some(len) = headers.get("content-length") {
            let len = len.to_str()?.parse::<u64>()?;
            Body::from(StreamingBody::new(self.stream.take(len)))
        } else {
            Body::empty()
        };
        
        // Create response
        let response = Response::new(body);
        Ok(response)
    }

    async fn read_headers(&mut self) -> Result<HeaderMap, Error> {
        let mut headers = HeaderMap::new();
        
        loop {
            let line = self.read_line().await?;
            if line.is_empty() {
                break;
            }
            
            // Parse header line
            let (name, value) = parse_header(&line)?;
            headers.insert(name, value);
        }
        
        Ok(headers)
    }
}

struct StreamingBody<R> {
    reader: R,
    buffer: BytesMut,
}

impl<R: AsyncRead + Unpin> StreamingBody<R> {
    fn new(reader: R) -> Self {
        Self {
            reader,
            buffer: BytesMut::with_capacity(8 * 1024),
        }
    }
}

impl<R: AsyncRead + Unpin> Stream for StreamingBody<R> {
    type Item = Result<Bytes, Error>;

    fn poll_next(
        mut self: Pin<&mut Self>,
        cx: &mut Context<'_>,
    ) -> Poll<Option<Self::Item>> {
        let this = &mut *self;
        
        // Read into buffer
        match Pin::new(&mut this.reader).poll_read(cx, &mut this.buffer) {
            Poll::Ready(Ok(0)) => Poll::Ready(None),
            Poll::Ready(Ok(n)) => {
                let chunk = this.buffer.split_to(n).freeze();
                Poll::Ready(Some(Ok(chunk)))
            }
            Poll::Ready(Err(e)) => Poll::Ready(Some(Err(e.into()))),
            Poll::Pending => Poll::Pending,
        }
    }
}
```

---

## Caching Strategy

Implement efficient caching:

```rust
use lru::LruCache;
use tokio::sync::RwLock;
use std::hash::{Hash, Hasher};

pub struct CacheLayer {
    cache: RwLock<LruCache<CacheKey, CacheEntry>>,
    max_size: usize,
    ttl: Duration,
}

#[derive(Hash, Eq, PartialEq)]
struct CacheKey {
    method: Method,
    uri: String,
    headers: Vec<(String, String)>,
}

struct CacheEntry {
    response: Response<Body>,
    created_at: Instant,
}

impl CacheLayer {
    pub fn new(max_size: usize, ttl: Duration) -> Self {
        Self {
            cache: RwLock::new(LruCache::new(max_size)),
            max_size,
            ttl,
        }
    }

    pub async fn get_or_compute<F, Fut>(
        &self,
        request: &Request<Body>,
        compute: F,
    ) -> Result<Response<Body>, Error>
    where
        F: FnOnce() -> Fut,
        Fut: Future<Output = Result<Response<Body>, Error>>,
    {
        let key = self.create_cache_key(request);
        
        // Check cache
        if let Some(entry) = self.get_from_cache(&key).await {
            return Ok(entry);
        }
        
        // Compute new value
        let response = compute().await?;
        
        // Cache if cacheable
        if self.is_cacheable(&response) {
            self.insert_into_cache(key, response.clone()).await;
        }
        
        Ok(response)
    }

    async fn get_from_cache(&self, key: &CacheKey) -> Option<Response<Body>> {
        let cache = self.cache.read().await;
        
        if let Some(entry) = cache.get(key) {
            if entry.created_at.elapsed() < self.ttl {
                return Some(entry.response.clone());
            }
        }
        
        None
    }

    async fn insert_into_cache(&self, key: CacheKey, response: Response<Body>) {
        let mut cache = self.cache.write().await;
        
        let entry = CacheEntry {
            response,
            created_at: Instant::now(),
        };
        
        cache.put(key, entry);
    }

    fn is_cacheable(&self, response: &Response<Body>) -> bool {
        // Check cache control headers
        if let Some(cache_control) = response.headers().get("cache-control") {
            if cache_control.to_str()
                .map(|s| s.contains("no-store"))
                .unwrap_or(false)
            {
                return false;
            }
        }
        
        // Check response code
        response.status().is_success()
    }
}
```

---

## Performance Profiling

Add performance monitoring:

```rust
use tokio::time::Instant;
use metrics::{histogram, counter};

pub struct PerformanceMonitor {
    start_time: Instant,
    checkpoints: Vec<(String, Instant)>,
}

impl PerformanceMonitor {
    pub fn new() -> Self {
        Self {
            start_time: Instant::now(),
            checkpoints: Vec::new(),
        }
    }

    pub fn checkpoint(&mut self, name: &str) {
        self.checkpoints.push((name.to_string(), Instant::now()));
    }

    pub fn record_metrics(&self) {
        let mut last_time = self.start_time;
        
        for (name, time) in &self.checkpoints {
            let duration = time.duration_since(last_time);
            
            histogram!(
                "request_phase_duration",
                duration.as_secs_f64(),
                "phase" => name.clone(),
            );
            
            last_time = *time;
        }
        
        let total_duration = last_time.duration_since(self.start_time);
        histogram!("request_total_duration", total_duration.as_secs_f64());
    }
}

#[async_trait]
impl<S> Layer<S> for PerformanceMonitor
where
    S: Service<Request<Body>, Response = Response<Body>> + Send + 'static,
    S::Future: Send,
{
    async fn call(&self, request: Request<Body>, service: &S) -> Result<Response<Body>> {
        let mut monitor = PerformanceMonitor::new();
        
        // Record pre-processing
        monitor.checkpoint("pre_processing");
        
        // Call service
        let response = service.call(request).await?;
        
        // Record post-processing
        monitor.checkpoint("post_processing");
        
        // Record metrics
        monitor.record_metrics();
        
        Ok(response)
    }
}
```

---

## Summary
- Implemented connection pooling
- Added request pipelining
- Optimized memory usage
- Created efficient caching
- Added performance monitoring

---

## Next Steps
Continue to [Module 15: Advanced Security](15-advanced-security.md) to learn about advanced security features.
