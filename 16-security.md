# Module 22: Security

## Learning Objectives
- Implement TLS/SSL encryption
- Add authentication mechanisms
- Create authorization layers
- Implement request validation
- Add security headers

## Prerequisites
- Completion of Module 21: Observability
- Understanding of cryptography
- Knowledge of security practices
- Familiarity with authentication methods

## Navigation
- [Previous: Observability](21-observability.md)
- [Next: DDoS Protection](23-ddos-protection.md)

---

## TLS/SSL Implementation

Implement TLS with rustls:

```rust
use rustls::{
    Certificate, PrivateKey, ServerConfig,
    server::{ClientHello, ResolvesServerCert},
    sign::CertifiedKey,
};
use std::sync::Arc;
use tokio_rustls::TlsAcceptor;

pub struct TlsManager {
    config: Arc<ServerConfig>,
    cert_resolver: Arc<CertResolver>,
}

impl TlsManager {
    pub fn new(
        certs_path: &str,
        key_path: &str,
    ) -> Result<Self, Box<dyn std::error::Error>> {
        // Load certificates
        let cert_chain = load_certs(certs_path)?;
        let key = load_private_key(key_path)?;
        
        // Create cert resolver
        let cert_resolver = Arc::new(CertResolver::new(
            cert_chain,
            key,
        ));
        
        // Configure TLS
        let config = ServerConfig::builder()
            .with_safe_defaults()
            .with_no_client_auth()
            .with_cert_resolver(cert_resolver.clone());
            
        Ok(Self {
            config: Arc::new(config),
            cert_resolver,
        })
    }

    pub fn get_acceptor(&self) -> TlsAcceptor {
        TlsAcceptor::from(self.config.clone())
    }

    pub fn update_certificates(
        &self,
        certs_path: &str,
        key_path: &str,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let cert_chain = load_certs(certs_path)?;
        let key = load_private_key(key_path)?;
        
        self.cert_resolver.update(cert_chain, key);
        Ok(())
    }
}

struct CertResolver {
    cert_key: RwLock<CertifiedKey>,
}

impl CertResolver {
    fn new(
        cert_chain: Vec<Certificate>,
        key: PrivateKey,
    ) -> Self {
        let cert_key = CertifiedKey::new(
            cert_chain,
            Arc::new(key),
        );
        
        Self {
            cert_key: RwLock::new(cert_key),
        }
    }

    fn update(
        &self,
        cert_chain: Vec<Certificate>,
        key: PrivateKey,
    ) {
        let cert_key = CertifiedKey::new(
            cert_chain,
            Arc::new(key),
        );
        
        *self.cert_key.write().unwrap() = cert_key;
    }
}

impl ResolvesServerCert for CertResolver {
    fn resolve(
        &self,
        _client_hello: ClientHello,
    ) -> Option<Arc<CertifiedKey>> {
        Some(Arc::new(
            self.cert_key.read().unwrap().clone(),
        ))
    }
}

fn load_certs(
    path: &str,
) -> Result<Vec<Certificate>, Box<dyn std::error::Error>> {
    let cert_file = std::fs::File::open(path)?;
    let mut reader = std::io::BufReader::new(cert_file);
    
    let certs = rustls_pemfile::certs(&mut reader)?
        .into_iter()
        .map(Certificate)
        .collect();
        
    Ok(certs)
}

fn load_private_key(
    path: &str,
) -> Result<PrivateKey, Box<dyn std::error::Error>> {
    let key_file = std::fs::File::open(path)?;
    let mut reader = std::io::BufReader::new(key_file);
    
    let key = rustls_pemfile::pkcs8_private_keys(&mut reader)?
        .remove(0);
        
    Ok(PrivateKey(key))
}
```

---

## Authentication

Implement authentication mechanisms:

```rust
use argon2::{
    password_hash::{PasswordHash, PasswordHasher, PasswordVerifier},
    Argon2,
};
use jsonwebtoken::{
    decode, encode,
    DecodingKey, EncodingKey,
    Header, Validation,
};
use serde::{Deserialize, Serialize};
use std::time::{SystemTime, UNIX_EPOCH};

#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    sub: String,
    exp: usize,
    roles: Vec<String>,
}

pub struct AuthManager {
    jwt_secret: Vec<u8>,
    token_duration: Duration,
}

impl AuthManager {
    pub fn new(
        jwt_secret: &[u8],
        token_duration: Duration,
    ) -> Self {
        Self {
            jwt_secret: jwt_secret.to_vec(),
            token_duration,
        }
    }

    pub async fn authenticate(
        &self,
        username: &str,
        password: &str,
    ) -> Result<String, AuthError> {
        // Get user from database
        let user = get_user(username).await?;
        
        // Verify password
        let parsed_hash = PasswordHash::new(&user.password_hash)
            .map_err(|_| AuthError::InvalidCredentials)?;
            
        Argon2::default()
            .verify_password(
                password.as_bytes(),
                &parsed_hash,
            )
            .map_err(|_| AuthError::InvalidCredentials)?;
            
        // Generate JWT
        self.create_token(username, &user.roles)
    }

    pub fn verify_token(
        &self,
        token: &str,
    ) -> Result<Claims, AuthError> {
        let key = DecodingKey::from_secret(&self.jwt_secret);
        let validation = Validation::default();
        
        let token_data = decode::<Claims>(
            token,
            &key,
            &validation,
        )
        .map_err(|_| AuthError::InvalidToken)?;
        
        Ok(token_data.claims)
    }

    fn create_token(
        &self,
        username: &str,
        roles: &[String],
    ) -> Result<String, AuthError> {
        let expiration = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs() as usize
            + self.token_duration.as_secs() as usize;
            
        let claims = Claims {
            sub: username.to_string(),
            exp: expiration,
            roles: roles.to_vec(),
        };
        
        let key = EncodingKey::from_secret(&self.jwt_secret);
        encode(
            &Header::default(),
            &claims,
            &key,
        )
        .map_err(|_| AuthError::TokenCreation)
    }
}

#[derive(Debug)]
pub enum AuthError {
    InvalidCredentials,
    InvalidToken,
    TokenCreation,
    DatabaseError,
}
```

---

## Authorization

Implement role-based authorization:

```rust
use std::collections::HashSet;
use hyper::{Response, StatusCode};

pub struct AuthorizationManager {
    role_permissions: HashMap<String, HashSet<String>>,
}

impl AuthorizationManager {
    pub fn new() -> Self {
        Self {
            role_permissions: HashMap::new(),
        }
    }

    pub fn add_role_permission(
        &mut self,
        role: &str,
        permission: &str,
    ) {
        self.role_permissions
            .entry(role.to_string())
            .or_default()
            .insert(permission.to_string());
    }

    pub fn check_permission(
        &self,
        roles: &[String],
        required_permission: &str,
    ) -> bool {
        roles.iter().any(|role| {
            self.role_permissions
                .get(role)
                .map(|permissions| {
                    permissions.contains(required_permission)
                })
                .unwrap_or(false)
        })
    }
}

pub struct AuthorizationMiddleware<S> {
    inner: S,
    auth_manager: Arc<AuthManager>,
    authz_manager: Arc<AuthorizationManager>,
}

impl<S> AuthorizationMiddleware<S> {
    pub fn new(
        inner: S,
        auth_manager: Arc<AuthManager>,
        authz_manager: Arc<AuthorizationManager>,
    ) -> Self {
        Self {
            inner,
            auth_manager,
            authz_manager,
        }
    }

    async fn check_auth(
        &self,
        request: &Request<Body>,
        required_permission: &str,
    ) -> Result<Claims, Response<Body>> {
        // Get token from header
        let auth_header = request
            .headers()
            .get("Authorization")
            .ok_or_else(|| {
                Response::builder()
                    .status(StatusCode::UNAUTHORIZED)
                    .body(Body::empty())
                    .unwrap()
            })?;
            
        let token = auth_header
            .to_str()
            .map_err(|_| {
                Response::builder()
                    .status(StatusCode::BAD_REQUEST)
                    .body(Body::empty())
                    .unwrap()
            })?
            .strip_prefix("Bearer ")
            .ok_or_else(|| {
                Response::builder()
                    .status(StatusCode::BAD_REQUEST)
                    .body(Body::empty())
                    .unwrap()
            })?;
            
        // Verify token
        let claims = self
            .auth_manager
            .verify_token(token)
            .map_err(|_| {
                Response::builder()
                    .status(StatusCode::UNAUTHORIZED)
                    .body(Body::empty())
                    .unwrap()
            })?;
            
        // Check permissions
        if !self.authz_manager.check_permission(
            &claims.roles,
            required_permission,
        ) {
            return Err(Response::builder()
                .status(StatusCode::FORBIDDEN)
                .body(Body::empty())
                .unwrap());
        }
        
        Ok(claims)
    }
}
```

---

## Request Validation

Implement request validation:

```rust
use regex::Regex;
use serde::Deserialize;
use validator::{Validate, ValidationError};

#[derive(Debug, Deserialize, Validate)]
pub struct RequestValidation {
    #[validate(length(min = 1, max = 100))]
    pub path: String,
    
    #[validate(regex = "SAFE_HEADERS")]
    pub headers: HashMap<String, String>,
    
    #[validate(range(min = 1, max = 1000000))]
    pub content_length: usize,
    
    #[validate(custom = "validate_content_type")]
    pub content_type: String,
}

lazy_static! {
    static ref SAFE_HEADERS: Regex =
        Regex::new(r"^[a-zA-Z0-9-]+$").unwrap();
}

fn validate_content_type(
    content_type: &str,
) -> Result<(), ValidationError> {
    let allowed_types = [
        "application/json",
        "application/x-www-form-urlencoded",
        "multipart/form-data",
        "text/plain",
    ];
    
    if allowed_types.contains(&content_type) {
        Ok(())
    } else {
        Err(ValidationError::new("invalid_content_type"))
    }
}

pub struct RequestValidator {
    max_uri_length: usize,
    max_header_count: usize,
    max_header_size: usize,
    blocked_ips: HashSet<IpAddr>,
}

impl RequestValidator {
    pub fn new(
        max_uri_length: usize,
        max_header_count: usize,
        max_header_size: usize,
    ) -> Self {
        Self {
            max_uri_length,
            max_header_count,
            max_header_size,
            blocked_ips: HashSet::new(),
        }
    }

    pub fn validate_request(
        &self,
        request: &Request<Body>,
    ) -> Result<(), ValidationError> {
        // Validate URI length
        if request.uri().to_string().len() > self.max_uri_length {
            return Err(ValidationError::new("uri_too_long"));
        }
        
        // Validate headers
        let headers = request.headers();
        if headers.len() > self.max_header_count {
            return Err(ValidationError::new("too_many_headers"));
        }
        
        for (name, value) in headers {
            if value.len() > self.max_header_size {
                return Err(ValidationError::new("header_too_large"));
            }
            
            // Check for unsafe characters
            if !SAFE_HEADERS.is_match(name.as_str()) {
                return Err(ValidationError::new("invalid_header_name"));
            }
        }
        
        // Check blocked IPs
        if let Some(addr) = request.extensions().get::<IpAddr>() {
            if self.blocked_ips.contains(addr) {
                return Err(ValidationError::new("blocked_ip"));
            }
        }
        
        Ok(())
    }

    pub fn block_ip(&mut self, ip: IpAddr) {
        self.blocked_ips.insert(ip);
    }

    pub fn unblock_ip(&mut self, ip: &IpAddr) {
        self.blocked_ips.remove(ip);
    }
}
```

---

## Security Headers

Add security headers middleware:

```rust
use hyper::{Body, Request, Response};
use tower::{Layer, Service};

pub struct SecurityHeadersLayer;

impl<S> Layer<S> for SecurityHeadersLayer {
    type Service = SecurityHeadersService<S>;

    fn layer(&self, service: S) -> Self::Service {
        SecurityHeadersService { inner: service }
    }
}

pub struct SecurityHeadersService<S> {
    inner: S,
}

impl<S> Service<Request<Body>> for SecurityHeadersService<S>
where
    S: Service<Request<Body>, Response = Response<Body>>,
{
    type Response = S::Response;
    type Error = S::Error;
    type Future = S::Future;

    fn poll_ready(
        &mut self,
        cx: &mut Context<'_>,
    ) -> Poll<Result<(), Self::Error>> {
        self.inner.poll_ready(cx)
    }

    fn call(&mut self, request: Request<Body>) -> Self::Future {
        let fut = self.inner.call(request);
        
        async move {
            let mut response = fut.await?;
            
            let headers = response.headers_mut();
            
            // Add security headers
            headers.insert(
                "X-Frame-Options",
                "DENY".parse().unwrap(),
            );
            
            headers.insert(
                "X-Content-Type-Options",
                "nosniff".parse().unwrap(),
            );
            
            headers.insert(
                "X-XSS-Protection",
                "1; mode=block".parse().unwrap(),
            );
            
            headers.insert(
                "Strict-Transport-Security",
                "max-age=31536000; includeSubDomains"
                    .parse()
                    .unwrap(),
            );
            
            headers.insert(
                "Content-Security-Policy",
                "default-src 'self'"
                    .parse()
                    .unwrap(),
            );
            
            headers.insert(
                "Referrer-Policy",
                "strict-origin-when-cross-origin"
                    .parse()
                    .unwrap(),
            );
            
            Ok(response)
        }
    }
}
```

---

## Summary
- Implemented TLS/SSL encryption
- Added authentication mechanisms
- Created authorization system
- Implemented request validation
- Added security headers

---

## Next Steps
Continue to [Module 23: DDoS Protection](23-ddos-protection.md) to learn about implementing DDoS protection features.
