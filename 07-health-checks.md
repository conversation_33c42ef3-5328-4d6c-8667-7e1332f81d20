# Module 05: Health Checks and Backend Monitoring

## Learning Objectives
- Understand the importance of backend health checks
- Implement active and passive health checks in Rust
- Integrate health monitoring with the round-robin selector
- Discuss trade-offs and failure handling strategies

## Prerequisites
- Completion of Module 04: Round-Robin Load Balancing
- Familiarity with Rust's concurrency and async primitives
- Understanding of basic HTTP request/response handling

## Navigation
- [Previous: Round-Robin Load Balancing](04-round-robin-balancing.md)
- [Next: Configuration System](06-configuration-system.md)

---

## Why Health Checks?

A robust load balancer must avoid routing traffic to failed or unhealthy backends. Health checks help detect failures and remove problematic servers from the rotation.

**Types of Health Checks:**
- **Active:** Periodically send test requests (e.g., HTTP GET /health) to each backend.
- **Passive:** Mark a backend as unhealthy if real client requests fail.

---

## Implementation: Active Health Checks

We'll spawn a background task that periodically checks each backend and updates their health status.

```rust
use std::sync::{Arc, Mutex};
use std::collections::HashMap;
use std::net::SocketAddr;
use tokio::time::{interval, Duration};
use hyper::{Client, Body, Request};

pub struct HealthMonitor {
    health: Arc<Mutex<HashMap<SocketAddr, bool>>>,
}

impl HealthMonitor {
    pub fn new(backends: &[SocketAddr]) -> Self {
        let health = backends.iter().map(|&b| (b, true)).collect();
        Self { health: Arc::new(Mutex::new(health)) }
    }

    pub fn is_healthy(&self, addr: &SocketAddr) -> bool {
        *self.health.lock().unwrap().get(addr).unwrap_or(&false)
    }

    pub fn health_map(&self) -> Arc<Mutex<HashMap<SocketAddr, bool>>> {
        Arc::clone(&self.health)
    }

    pub async fn start(self, backends: Vec<SocketAddr>) {
        let health = self.health;
        tokio::spawn(async move {
            let client = Client::new();
            let mut interval = interval(Duration::from_secs(5));
            loop {
                interval.tick().await;
                for &backend in &backends {
                    let req = Request::builder()
                        .uri(format!("http://{}/health", backend))
                        .body(Body::empty())
                        .unwrap();
                    let healthy = client.request(req).await.is_ok();
                    health.lock().unwrap().insert(backend, healthy);
                }
            }
        });
    }
}
```

---

## Integrating Health Checks with Round-Robin

Update your round-robin selector to skip unhealthy backends:

```rust
pub fn next_healthy(&self, health: &HealthMonitor) -> Option<SocketAddr> {
    let mut idx = self.index.lock().unwrap();
    for _ in 0..self.backends.len() {
        let addr = self.backends[*idx];
        *idx = (*idx + 1) % self.backends.len();
        if health.is_healthy(&addr) {
            return Some(addr);
        }
    }
    None // No healthy backends
}
```

---

## Passive Health Checks

If a real request to a backend fails, mark it as unhealthy for a cooldown period. This can be combined with active checks for better reliability.

---

## Summary
- Health checks prevent routing to failed servers
- Combine active and passive checks for robustness
- Integrate health status with backend selection

---

## Next Steps
Continue to [Module 06: Configuration System](06-configuration-system.md) to make your load balancer flexible and user-configurable.
