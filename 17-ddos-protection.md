# Module 23: DDoS Protection

## Learning Objectives
- Implement rate limiting strategies
- Add IP blacklisting
- Create request filtering
- Implement traffic analysis
- Add DDoS mitigation

## Prerequisites
- Completion of Module 22: Security
- Understanding of DDoS attacks
- Knowledge of rate limiting
- Familiarity with traffic analysis

## Navigation
- [Previous: Security](22-security.md)
- [Next: Logging and Analytics](24-logging-analytics.md)

---

## Advanced Rate Limiting

Implement advanced rate limiting strategies:

```rust
use std::collections::HashMap;
use std::net::IpAddr;
use std::sync::Arc;
use tokio::sync::RwLock;
use std::time::{Duration, Instant};

pub struct DdosProtection {
    ip_limits: Arc<RwLock<HashMap<IpAddr, RateLimiter>>>,
    path_limits: Arc<RwLock<HashMap<String, RateLimiter>>>,
    global_limit: RateLimiter,
    burst_detector: BurstDetector,
    blacklist: Arc<RwLock<BlackList>>,
}

impl DdosProtection {
    pub fn new(
        ip_rate: u32,
        path_rate: u32,
        global_rate: u32,
        window: Duration,
    ) -> Self {
        Self {
            ip_limits: Arc::new(RwLock::new(HashMap::new())),
            path_limits: Arc::new(RwLock::new(HashMap::new())),
            global_limit: RateLimiter::new(global_rate, window),
            burst_detector: BurstDetector::new(
                Duration::from_secs(1),
                100,
            ),
            blacklist: Arc::new(RwLock::new(BlackList::new())),
        }
    }

    pub async fn check_request(
        &self,
        ip: IpAddr,
        path: &str,
    ) -> Result<(), DdosError> {
        // Check blacklist
        if self.blacklist.read().await.is_blacklisted(&ip) {
            return Err(DdosError::Blacklisted);
        }
        
        // Check global limit
        if !self.global_limit.check().await {
            return Err(DdosError::GlobalLimit);
        }
        
        // Check IP limit
        let mut ip_limits = self.ip_limits.write().await;
        let ip_limiter = ip_limits
            .entry(ip)
            .or_insert_with(|| {
                RateLimiter::new(100, Duration::from_secs(60))
            });
            
        if !ip_limiter.check().await {
            // Add to blacklist if repeatedly exceeding
            if ip_limiter.violations() > 5 {
                self.blacklist
                    .write()
                    .await
                    .add_ip(ip, Duration::from_mins(10));
            }
            return Err(DdosError::IpLimit);
        }
        
        // Check path limit
        let mut path_limits = self.path_limits.write().await;
        let path_limiter = path_limits
            .entry(path.to_string())
            .or_insert_with(|| {
                RateLimiter::new(1000, Duration::from_secs(60))
            });
            
        if !path_limiter.check().await {
            return Err(DdosError::PathLimit);
        }
        
        // Check for burst
        if self.burst_detector.is_burst(ip).await {
            self.blacklist
                .write()
                .await
                .add_ip(ip, Duration::from_mins(5));
            return Err(DdosError::BurstDetected);
        }
        
        Ok(())
    }
}

pub struct RateLimiter {
    rate: u32,
    window: Duration,
    requests: Vec<Instant>,
    violations: u32,
}

impl RateLimiter {
    pub fn new(rate: u32, window: Duration) -> Self {
        Self {
            rate,
            window,
            requests: Vec::new(),
            violations: 0,
        }
    }

    pub async fn check(&mut self) -> bool {
        let now = Instant::now();
        
        // Remove old requests
        self.requests.retain(|&time| {
            now.duration_since(time) <= self.window
        });
        
        if self.requests.len() >= self.rate as usize {
            self.violations += 1;
            false
        } else {
            self.requests.push(now);
            true
        }
    }

    pub fn violations(&self) -> u32 {
        self.violations
    }
}

pub struct BurstDetector {
    window: Duration,
    threshold: u32,
    requests: Arc<RwLock<HashMap<IpAddr, Vec<Instant>>>>,
}

impl BurstDetector {
    pub fn new(window: Duration, threshold: u32) -> Self {
        Self {
            window,
            threshold,
            requests: Arc::new(RwLock::new(HashMap::new())),
        }
    }

    pub async fn is_burst(&self, ip: IpAddr) -> bool {
        let now = Instant::now();
        let mut requests = self.requests.write().await;
        
        let ip_requests = requests
            .entry(ip)
            .or_insert_with(Vec::new);
            
        // Remove old requests
        ip_requests.retain(|&time| {
            now.duration_since(time) <= self.window
        });
        
        // Add new request
        ip_requests.push(now);
        
        // Check burst
        ip_requests.len() >= self.threshold as usize
    }
}
```

---

## IP Blacklisting

Implement IP blacklisting:

```rust
use std::collections::HashMap;
use std::net::IpAddr;
use std::time::{Duration, Instant};

pub struct BlackList {
    blocked_ips: HashMap<IpAddr, BlackListEntry>,
}

struct BlackListEntry {
    start_time: Instant,
    duration: Duration,
}

impl BlackList {
    pub fn new() -> Self {
        Self {
            blocked_ips: HashMap::new(),
        }
    }

    pub fn add_ip(&mut self, ip: IpAddr, duration: Duration) {
        self.blocked_ips.insert(
            ip,
            BlackListEntry {
                start_time: Instant::now(),
                duration,
            },
        );
    }

    pub fn is_blacklisted(&self, ip: &IpAddr) -> bool {
        if let Some(entry) = self.blocked_ips.get(ip) {
            let elapsed = Instant::now()
                .duration_since(entry.start_time);
            elapsed < entry.duration
        } else {
            false
        }
    }

    pub fn cleanup(&mut self) {
        let now = Instant::now();
        self.blocked_ips.retain(|_, entry| {
            now.duration_since(entry.start_time) < entry.duration
        });
    }
}

pub struct BlackListManager {
    blacklist: Arc<RwLock<BlackList>>,
    persistence: BlackListPersistence,
}

impl BlackListManager {
    pub fn new(
        persistence: BlackListPersistence,
    ) -> Self {
        Self {
            blacklist: Arc::new(RwLock::new(BlackList::new())),
            persistence,
        }
    }

    pub async fn start_cleanup_task(self: Arc<Self>) {
        let mut interval = tokio::time::interval(
            Duration::from_secs(60),
        );
        
        loop {
            interval.tick().await;
            self.cleanup().await;
        }
    }

    async fn cleanup(&self) {
        let mut blacklist = self.blacklist.write().await;
        blacklist.cleanup();
        
        // Persist changes
        if let Err(e) = self
            .persistence
            .save_blacklist(&blacklist)
            .await
        {
            error!("Failed to persist blacklist: {}", e);
        }
    }

    pub async fn add_ip(
        &self,
        ip: IpAddr,
        duration: Duration,
        reason: &str,
    ) {
        let mut blacklist = self.blacklist.write().await;
        blacklist.add_ip(ip, duration);
        
        // Log blacklist event
        info!(
            "IP {} blacklisted for {} seconds: {}",
            ip,
            duration.as_secs(),
            reason,
        );
        
        // Persist changes
        if let Err(e) = self
            .persistence
            .save_blacklist(&blacklist)
            .await
        {
            error!("Failed to persist blacklist: {}", e);
        }
    }
}
```

---

## Request Filtering

Implement request filtering:

```rust
use http::HeaderMap;
use regex::Regex;

pub struct RequestFilter {
    user_agent_patterns: Vec<Regex>,
    path_patterns: Vec<Regex>,
    header_rules: Vec<HeaderRule>,
    payload_rules: Vec<PayloadRule>,
}

struct HeaderRule {
    name: String,
    pattern: Regex,
    action: FilterAction,
}

struct PayloadRule {
    pattern: Regex,
    action: FilterAction,
}

#[derive(Clone, Copy)]
enum FilterAction {
    Block,
    Log,
    RateLimit,
}

impl RequestFilter {
    pub fn new() -> Self {
        Self {
            user_agent_patterns: Vec::new(),
            path_patterns: Vec::new(),
            header_rules: Vec::new(),
            payload_rules: Vec::new(),
        }
    }

    pub fn add_user_agent_pattern(
        &mut self,
        pattern: &str,
    ) -> Result<(), regex::Error> {
        let regex = Regex::new(pattern)?;
        self.user_agent_patterns.push(regex);
        Ok(())
    }

    pub fn add_path_pattern(
        &mut self,
        pattern: &str,
    ) -> Result<(), regex::Error> {
        let regex = Regex::new(pattern)?;
        self.path_patterns.push(regex);
        Ok(())
    }

    pub fn add_header_rule(
        &mut self,
        name: &str,
        pattern: &str,
        action: FilterAction,
    ) -> Result<(), regex::Error> {
        let regex = Regex::new(pattern)?;
        self.header_rules.push(HeaderRule {
            name: name.to_string(),
            pattern: regex,
            action,
        });
        Ok(())
    }

    pub fn add_payload_rule(
        &mut self,
        pattern: &str,
        action: FilterAction,
    ) -> Result<(), regex::Error> {
        let regex = Regex::new(pattern)?;
        self.payload_rules.push(PayloadRule {
            pattern: regex,
            action,
        });
        Ok(())
    }

    pub fn check_request(
        &self,
        headers: &HeaderMap,
        path: &str,
        payload: &[u8],
    ) -> Vec<FilterAction> {
        let mut actions = Vec::new();
        
        // Check User-Agent
        if let Some(ua) = headers.get("user-agent") {
            if let Ok(ua_str) = ua.to_str() {
                for pattern in &self.user_agent_patterns {
                    if pattern.is_match(ua_str) {
                        actions.push(FilterAction::Block);
                    }
                }
            }
        }
        
        // Check path
        for pattern in &self.path_patterns {
            if pattern.is_match(path) {
                actions.push(FilterAction::Block);
            }
        }
        
        // Check headers
        for rule in &self.header_rules {
            if let Some(value) = headers.get(&rule.name) {
                if let Ok(value_str) = value.to_str() {
                    if rule.pattern.is_match(value_str) {
                        actions.push(rule.action);
                    }
                }
            }
        }
        
        // Check payload
        let payload_str = String::from_utf8_lossy(payload);
        for rule in &self.payload_rules {
            if rule.pattern.is_match(&payload_str) {
                actions.push(rule.action);
            }
        }
        
        actions
    }
}
```

---

## Traffic Analysis

Implement traffic analysis:

```rust
use std::collections::VecDeque;
use std::time::{Duration, Instant};
use tokio::sync::broadcast;

pub struct TrafficAnalyzer {
    window_size: Duration,
    samples: VecDeque<TrafficSample>,
    anomaly_tx: broadcast::Sender<AnomalyEvent>,
}

struct TrafficSample {
    timestamp: Instant,
    requests: u32,
    bytes: u64,
    errors: u32,
}

#[derive(Clone, Debug)]
pub struct AnomalyEvent {
    pub timestamp: Instant,
    pub anomaly_type: AnomalyType,
    pub details: String,
}

#[derive(Clone, Debug)]
pub enum AnomalyType {
    HighTraffic,
    ErrorSpike,
    UnusualPattern,
}

impl TrafficAnalyzer {
    pub fn new(window_size: Duration) -> Self {
        let (tx, _) = broadcast::channel(100);
        
        Self {
            window_size,
            samples: VecDeque::new(),
            anomaly_tx: tx,
        }
    }

    pub fn subscribe(&self) -> broadcast::Receiver<AnomalyEvent> {
        self.anomaly_tx.subscribe()
    }

    pub fn add_sample(
        &mut self,
        requests: u32,
        bytes: u64,
        errors: u32,
    ) {
        let now = Instant::now();
        
        // Remove old samples
        while let Some(sample) = self.samples.front() {
            if now.duration_since(sample.timestamp)
                > self.window_size
            {
                self.samples.pop_front();
            } else {
                break;
            }
        }
        
        // Add new sample
        self.samples.push_back(TrafficSample {
            timestamp: now,
            requests,
            bytes,
            errors,
        });
        
        // Analyze traffic
        self.analyze();
    }

    fn analyze(&mut self) {
        if self.samples.len() < 2 {
            return;
        }
        
        let now = Instant::now();
        
        // Calculate metrics
        let total_requests: u32 = self
            .samples
            .iter()
            .map(|s| s.requests)
            .sum();
            
        let avg_requests =
            total_requests as f64 / self.samples.len() as f64;
            
        let total_errors: u32 = self
            .samples
            .iter()
            .map(|s| s.errors)
            .sum();
            
        let error_rate =
            total_errors as f64 / total_requests as f64;
            
        // Check for anomalies
        if avg_requests > 1000.0 {
            self.anomaly_tx
                .send(AnomalyEvent {
                    timestamp: now,
                    anomaly_type: AnomalyType::HighTraffic,
                    details: format!(
                        "High traffic: {} req/s",
                        avg_requests
                    ),
                })
                .ok();
        }
        
        if error_rate > 0.1 {
            self.anomaly_tx
                .send(AnomalyEvent {
                    timestamp: now,
                    anomaly_type: AnomalyType::ErrorSpike,
                    details: format!(
                        "High error rate: {:.2}%",
                        error_rate * 100.0
                    ),
                })
                .ok();
        }
        
        // Check for unusual patterns
        if let Some(pattern) = self.detect_unusual_pattern() {
            self.anomaly_tx
                .send(AnomalyEvent {
                    timestamp: now,
                    anomaly_type: AnomalyType::UnusualPattern,
                    details: pattern,
                })
                .ok();
        }
    }

    fn detect_unusual_pattern(&self) -> Option<String> {
        // Implement pattern detection logic
        // For example, sudden spikes or drops
        None
    }
}
```

---

## DDoS Mitigation

Implement DDoS mitigation strategies:

```rust
use std::collections::HashMap;
use std::net::IpAddr;
use std::sync::Arc;
use tokio::sync::RwLock;

pub struct DdosMitigation {
    traffic_analyzer: Arc<RwLock<TrafficAnalyzer>>,
    blacklist_manager: Arc<BlackListManager>,
    request_filter: Arc<RequestFilter>,
    ddos_protection: Arc<DdosProtection>,
    mitigation_rules: Vec<MitigationRule>,
}

struct MitigationRule {
    condition: Box<dyn Fn(&AnomalyEvent) -> bool + Send + Sync>,
    action: MitigationAction,
}

enum MitigationAction {
    IncreaseRateLimits,
    EnableCaptcha,
    BlockTraffic,
    ScaleResources,
}

impl DdosMitigation {
    pub fn new(
        traffic_analyzer: Arc<RwLock<TrafficAnalyzer>>,
        blacklist_manager: Arc<BlackListManager>,
        request_filter: Arc<RequestFilter>,
        ddos_protection: Arc<DdosProtection>,
    ) -> Self {
        Self {
            traffic_analyzer,
            blacklist_manager,
            request_filter,
            ddos_protection,
            mitigation_rules: Vec::new(),
        }
    }

    pub fn add_rule(
        &mut self,
        condition: Box<dyn Fn(&AnomalyEvent) -> bool + Send + Sync>,
        action: MitigationAction,
    ) {
        self.mitigation_rules.push(MitigationRule {
            condition,
            action,
        });
    }

    pub async fn start(self: Arc<Self>) {
        let mut rx = self
            .traffic_analyzer
            .read()
            .await
            .subscribe();
            
        while let Ok(event) = rx.recv().await {
            self.handle_anomaly(event).await;
        }
    }

    async fn handle_anomaly(&self, event: AnomalyEvent) {
        for rule in &self.mitigation_rules {
            if (rule.condition)(&event) {
                self.apply_mitigation(&rule.action).await;
            }
        }
    }

    async fn apply_mitigation(
        &self,
        action: &MitigationAction,
    ) {
        match action {
            MitigationAction::IncreaseRateLimits => {
                // Implement rate limit adjustment
            }
            MitigationAction::EnableCaptcha => {
                // Implement CAPTCHA
            }
            MitigationAction::BlockTraffic => {
                // Implement traffic blocking
            }
            MitigationAction::ScaleResources => {
                // Implement resource scaling
            }
        }
    }
}
```

---

## Summary
- Implemented advanced rate limiting
- Added IP blacklisting
- Created request filtering
- Implemented traffic analysis
- Added DDoS mitigation strategies

---

## Next Steps
Continue to [Module 24: Logging and Analytics](24-logging-analytics.md) to learn about implementing logging and analytics features.
