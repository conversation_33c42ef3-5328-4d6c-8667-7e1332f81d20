# 13 - Weighted Load Balancing

## Goal
Support weighted round-robin and least-connections algorithms.

## Concepts Introduced
- Weighted algorithms
- Data structures for weights
- Fairness and starvation prevention

## Why Weighted Balancing?
It allows prioritizing certain backends, useful for heterogeneous environments.

## Alternatives Considered
- Equal distribution (simpler, but less flexible)
- External traffic shaping (outside the load balancer)

## Next Steps
We'll add sticky sessions for session affinity.
