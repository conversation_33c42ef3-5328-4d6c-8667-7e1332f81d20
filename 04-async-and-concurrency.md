# 04 - Async and Concurrency

## Goal
Refactor our proxy to handle multiple connections concurrently using Rust's async ecosystem.

## Concepts Introduced
- `tokio` or `async-std` for async I/O
- Futures and async/await syntax
- Spawning tasks

## Why Async?
Synchronous code can't handle many clients efficiently. Async Rust allows scalable, performant network services.

## Alternatives Considered
- Thread-per-connection (simple, but not scalable)
- Using only sync code (limits performance)

## Next Steps
We'll add configuration and logging to make our load balancer production-ready.
