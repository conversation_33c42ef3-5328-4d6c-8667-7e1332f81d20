# Module 06: Configuration System

## Learning Objectives
- Design a flexible configuration system for the load balancer
- Implement configuration loading from files and environment variables
- Add hot-reload capability for configuration changes
- Handle configuration validation and errors

## Prerequisites
- Completion of Module 05: Health Checks and Backend Monitoring
- Understanding of Rust's serde library for serialization
- Familiarity with error handling and configuration patterns

## Navigation
- [Previous: Health Checks and Backend Monitoring](05-health-checks.md)
- [Next: Logging and Metrics](07-logging-metrics.md)

---

## Configuration Requirements

A production-grade load balancer needs flexible configuration options:

**Key Configuration Areas:**
- Backend server addresses and ports
- Health check settings (intervals, timeouts)
- Load balancing algorithm parameters
- TLS/SSL settings
- Logging levels and outputs
- Resource limits and timeouts

---

## Implementation: Configuration System

First, let's define our configuration structures using serde for serialization:

```rust
use serde::{Deserialize, Serialize};
use std::net::SocketAddr;
use std::time::Duration;

#[derive(Debug, Serialize, Deserialize)]
pub struct LoadBalancerConfig {
    /// List of backend server addresses
    pub backends: Vec<BackendConfig>,
    
    /// Health check configuration
    pub health_check: HealthCheckConfig,
    
    /// Server bind address
    pub bind_addr: SocketAddr,
    
    /// Maximum connections per backend
    #[serde(default = "default_max_connections")]
    pub max_connections: usize,
    
    /// Connection timeout
    #[serde(with = "duration_serde")]
    pub timeout: Duration,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct BackendConfig {
    pub address: SocketAddr,
    pub weight: u32,
    #[serde(default)]
    pub disabled: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct HealthCheckConfig {
    /// Health check interval in seconds
    pub interval: u64,
    
    /// Health check timeout in seconds
    pub timeout: u64,
    
    /// Health check endpoint path
    #[serde(default = "default_health_path")]
    pub path: String,
    
    /// Number of failures before marking backend as unhealthy
    #[serde(default = "default_failure_threshold")]
    pub failure_threshold: u32,
}

/// Configuration manager that handles loading and hot-reloading
pub struct ConfigManager {
    config: Arc<RwLock<LoadBalancerConfig>>,
    config_path: PathBuf,
}

impl ConfigManager {
    /// Create a new configuration manager
    pub fn new(config_path: PathBuf) -> Result<Self> {
        let config = Self::load_config(&config_path)?;
        Ok(Self {
            config: Arc::new(RwLock::new(config)),
            config_path,
        })
    }
    
    /// Load configuration from file
    fn load_config(path: &Path) -> Result<LoadBalancerConfig> {
        let content = std::fs::read_to_string(path)?;
        let config: LoadBalancerConfig = serde_yaml::from_str(&content)?;
        config.validate()?;
        Ok(config)
    }
    
    /// Start configuration hot-reload watcher
    pub async fn start_watcher(&self) -> Result<()> {
        let config = Arc::clone(&self.config);
        let path = self.config_path.clone();
        
        tokio::spawn(async move {
            let (tx, rx) = watch::channel(());
            let mut watcher = notify::recommended_watcher(move |res| {
                if let Ok(event) = res {
                    if event.kind.is_modify() {
                        let _ = tx.send(());
                    }
                }
            })?;
            
            watcher.watch(&path, notify::RecursiveMode::NonRecursive)?;
            
            while rx.changed().await.is_ok() {
                match Self::load_config(&path) {
                    Ok(new_config) => {
                        *config.write().await = new_config;
                        info!("Configuration reloaded successfully");
                    }
                    Err(e) => {
                        error!("Failed to reload configuration: {}", e);
                    }
                }
            }
            
            Ok::<(), Error>(())
        });
        
        Ok(())
    }
}

impl LoadBalancerConfig {
    /// Validate configuration values
    fn validate(&self) -> Result<()> {
        if self.backends.is_empty() {
            return Err(Error::Configuration("No backend servers configured"));
        }
        
        if self.health_check.interval == 0 {
            return Err(Error::Configuration("Health check interval must be > 0"));
        }
        
        if self.health_check.timeout >= self.health_check.interval {
            return Err(Error::Configuration("Health check timeout must be less than interval"));
        }
        
        Ok(())
    }
}

/// Default configuration values
fn default_max_connections() -> usize { 1000 }
fn default_health_path() -> String { "/health".to_string() }
fn default_failure_threshold() -> u32 { 3 }

/// Custom serialization for Duration
mod duration_serde {
    use serde::{Deserialize, Deserializer, Serializer};
    use std::time::Duration;

    pub fn serialize<S>(duration: &Duration, serializer: S) -> Result<S::Ok, S::Error>
    where
        S: Serializer,
    {
        serializer.serialize_u64(duration.as_secs())
    }

    pub fn deserialize<'de, D>(deserializer: D) -> Result<Duration, D::Error>
    where
        D: Deserializer<'de>,
    {
        let secs = u64::deserialize(deserializer)?;
        Ok(Duration::from_secs(secs))
    }
}
```

---

## Example Configuration File

Here's a sample YAML configuration file (`config.yml`):

```yaml
bind_addr: "127.0.0.1:8080"
backends:
  - address: "127.0.0.1:8081"
    weight: 1
  - address: "127.0.0.1:8082"
    weight: 2
    disabled: false
health_check:
  interval: 5
  timeout: 2
  path: "/health"
  failure_threshold: 3
max_connections: 1000
timeout: 30
```

---

## Using the Configuration System

Integrate the configuration system with your load balancer:

```rust
#[tokio::main]
async fn main() -> Result<()> {
    let config_manager = ConfigManager::new("config.yml".into())?;
    
    // Start configuration hot-reload watcher
    config_manager.start_watcher().await?;
    
    // Create load balancer with initial config
    let config = config_manager.get_config();
    let lb = LoadBalancer::new(config);
    
    // Start the load balancer
    lb.run().await
}
```

---

## Environment Variable Override

Allow configuration values to be overridden using environment variables:

```rust
impl LoadBalancerConfig {
    /// Override configuration with environment variables
    pub fn apply_env_overrides(&mut self) {
        if let Ok(addr) = std::env::var("LB_BIND_ADDR") {
            if let Ok(socket_addr) = addr.parse() {
                self.bind_addr = socket_addr;
            }
        }
        
        if let Ok(timeout) = std::env::var("LB_TIMEOUT")
            .ok()
            .and_then(|s| s.parse().ok())
        {
            self.timeout = Duration::from_secs(timeout);
        }
        
        // Add more environment variable overrides as needed
    }
}
```

---

## Summary
- Flexible configuration using YAML files
- Hot-reload support for dynamic updates
- Environment variable overrides
- Robust validation and error handling

---

## Next Steps
Continue to [Module 07: Logging and Metrics](07-logging-metrics.md) to implement observability features for your load balancer.
