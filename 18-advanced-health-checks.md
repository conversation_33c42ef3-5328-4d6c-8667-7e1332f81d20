# 16 - Graceful Shutdown

## Goal
Ensure the load balancer can shut down or restart without dropping active connections.

## Concepts Introduced
- Signal handling
- Connection draining
- Coordinated shutdown

## Why Graceful Shutdown?
It prevents data loss and improves reliability during upgrades or maintenance.

## Alternatives Considered
- Immediate shutdown (may drop connections)
- Relying on external orchestrators (less control)

## Next Steps
We'll add a plugin system for extensibility.
