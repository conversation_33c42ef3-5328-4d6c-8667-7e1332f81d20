# 09 - Metrics and Monitoring

## Goal
Add metrics and monitoring to observe the load balancer's performance and health.

## Concepts Introduced
- Metrics collection (requests, errors, latency)
- Exposing metrics endpoints
- Using crates like `prometheus`

## Why Metrics?
Observability is key for operating reliable systems. Metrics help detect issues and optimize performance.

## Alternatives Considered
- Logging only (less structured)
- No monitoring (not recommended)

## Next Steps
We'll add support for TLS and secure communication.
