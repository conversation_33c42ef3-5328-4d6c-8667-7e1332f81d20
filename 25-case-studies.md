# 18 - Advanced Health Checks

## Goal
Support HTTP(s) health checks, custom scripts, and active probing.

## Concepts Introduced
- HTTP client basics
- Script execution
- Customizable health logic

## Why Advanced Health Checks?
They provide more accurate backend status and support diverse environments.

## Alternatives Considered
- Simple TCP checks only (less informative)
- Relying on backend self-reporting (less reliable)

## Next Steps
We'll add access control and authentication features.
