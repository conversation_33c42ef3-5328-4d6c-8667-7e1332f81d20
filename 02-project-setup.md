# Module 01: Project Setup and Architecture

## Learning Objectives
- Set up a Rust development environment optimized for network programming
- Create and structure a load balancer project with proper architecture
- Understand load balancer design patterns and architectural decisions
- Configure essential dependencies for async networking and HTTP handling
- Design the foundational module structure for scalable development
- Implement basic project configuration and build system

## Prerequisites
- Basic familiarity with command line operations
- Text editor or IDE installed (VS Code with rust-analyzer recommended)
- Understanding of basic networking concepts (TCP, HTTP, ports)
- Rust fundamentals (variables, functions, structs, modules)

## Navigation
- [Previous: Introduction](00-introduction.md)
- [Next: Basic TCP Proxy](02-basic-tcp-proxy.md)


## Load Balancer Architecture Overview: Rationale and Strategy

Before diving into code, it's crucial to understand the "why" behind our architecture. Every design choice in a load balancer impacts scalability, reliability, and maintainability. We'll highlight the reasoning and trade-offs for each major decision.


### What is a Load Balancer? (And Why Build One?)

A load balancer is a system that distributes incoming requests across multiple backend servers. This is essential for:

1. **Traffic Distribution:** Prevents any single server from being overwhelmed, improving reliability and throughput.
2. **High Availability:** If a server fails, the load balancer can route around it, minimizing downtime.
3. **Scalability:** You can add or remove servers as demand changes, without affecting clients.
4. **Performance:** By routing requests intelligently (e.g., to the least-loaded server), you can optimize response times.
5. **Security:** The load balancer can act as a shield, hiding backend details and absorbing certain attacks.

**Why build your own?**
- You'll learn how real-world systems like NGINX, Envoy, and HAProxy work under the hood.
- You'll understand the trade-offs in design, performance, and security.
- You'll gain hands-on experience with Rust's strengths in systems programming.


### Load Balancer Types: Layer 4 vs Layer 7

```mermaid
flowchart TD
    subgraph "Layer 4 (Transport Layer)"
        L4[TCP/UDP Load Balancer]
        L4 --> |Fast, Protocol Agnostic| Backend1[Backend Server]
        L4 --> Backend2[Backend Server]
    end
    
    subgraph "Layer 7 (Application Layer)"
        L7[HTTP/HTTPS Load Balancer]
        L7 --> |Content-Aware Routing| API[API Servers]
        L7 --> Static[Static Content]
        L7 --> Dynamic[Dynamic Content]
    end
    
    Client --> L4
    Client --> L7
```


**Layer 4 (Transport Layer):**
- Operates at TCP/UDP level (e.g., forwarding raw TCP connections)
- Fast and protocol-agnostic (works for any protocol, not just HTTP)
- Lower latency, but can't make decisions based on application data (e.g., URL path)

**When to use Layer 4?**
- When you need maximum throughput and don't care about application-level routing.
- For protocols other than HTTP/HTTPS.


**Layer 7 (Application Layer):**
- Operates at HTTP/HTTPS level (understands requests, headers, cookies, etc.)
- Enables content-based routing, sticky sessions, header manipulation, etc.
- Slightly higher overhead, but much more flexible and powerful.

**When to use Layer 7?**
- When you need to route based on application data (e.g., API version, user cookies).
- For advanced features like authentication, compression, or caching.

**Our Approach:**
We'll start with a simple Layer 4 proxy (to learn the basics), then quickly move to Layer 7 HTTP load balancing for real-world power and flexibility.


### Architectural Decisions and Trade-offs: Why These Choices?


#### 1. Proxy vs. Direct Server Return (DSR)

**Proxy Mode (Chosen):**
- The load balancer sits in the middle, forwarding both requests and responses.
- **Pros:** Simpler to implement, easier to debug, enables SSL termination and request/response modification.
- **Cons:** All traffic flows through the load balancer, so it can become a bottleneck if not designed well.

**Direct Server Return (DSR):**
- The load balancer only handles incoming requests; servers respond directly to clients.
- **Pros:** Reduces bandwidth on the load balancer, can improve performance for large responses.
- **Cons:** More complex networking setup, harder to debug, can't modify responses or terminate SSL easily.

**Why Proxy Mode?**
- It's the most flexible and educational for a learning project, and is the most common approach in modern software load balancers.


#### 2. Synchronous vs. Asynchronous I/O

**Asynchronous I/O (Chosen):**
- Handles thousands of concurrent connections efficiently, using a small number of threads.
- **Pros:** Scales well, uses system resources efficiently, ideal for I/O-bound workloads like load balancing.
- **Cons:** More complex programming model, requires understanding of async/await and Rust's async ecosystem.

**Synchronous I/O:**
- Each connection gets its own thread (or blocks a thread).
- **Pros:** Simpler to reason about, easier for small scripts or tools.
- **Cons:** Doesn't scale to many connections, high memory and CPU usage.

**Why Async?**
- Modern load balancers must handle thousands of clients. Async is the only practical way to do this in Rust.


#### 3. Connection Handling Strategies

**Connection Pooling:**
- Reuse connections to backends to reduce latency and resource usage.
- **Why?** Creating new TCP connections is expensive. Pooling improves performance and scalability.

**Keep-Alive:**
- Maintain persistent connections to avoid reconnecting for every request.
- **Why?** Reduces connection overhead, especially for HTTP/1.1 and HTTP/2.

**Connection Multiplexing:**
- Share a single connection for multiple requests (especially with HTTP/2).
- **Why?** Further reduces resource usage and improves throughput.

**Alternatives:**
- No pooling (simpler, but much less efficient)
- Thread-per-connection (easy, but doesn't scale)

**Our Approach:**
- We'll start simple, then add pooling and keep-alive as we build more advanced modules.

## Setting Up the Development Environment

### Installing Rust and Tools

```bash
# Install Rust (if not already installed)
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# Verify installation
rustc --version
cargo --version

# Install useful development tools
cargo install cargo-watch    # Auto-rebuild on file changes
cargo install cargo-expand   # Expand macros for debugging
cargo install flamegraph     # Performance profiling
```

### Creating the Project

```bash
# Create new binary project
cargo new rusty-balancer --bin
cd rusty-balancer

# Initialize git repository
git init
echo "target/" > .gitignore
echo "Cargo.lock" >> .gitignore
```

### Project Structure Design

Let's design a modular architecture that will scale as we add features:

```
rusty-balancer/
├── Cargo.toml                 # Project configuration and dependencies
├── src/
│   ├── main.rs               # Application entry point
│   ├── lib.rs                # Library root for reusable components
│   ├── config/               # Configuration management
│   │   ├── mod.rs
│   │   └── settings.rs
│   ├── proxy/                # Core proxy functionality
│   │   ├── mod.rs
│   │   ├── tcp_proxy.rs      # TCP-level proxying
│   │   └── http_proxy.rs     # HTTP-level proxying
│   ├── balancer/             # Load balancing algorithms
│   │   ├── mod.rs
│   │   ├── round_robin.rs
│   │   ├── weighted.rs
│   │   └── least_connections.rs
│   ├── health/               # Health checking
│   │   ├── mod.rs
│   │   └── checker.rs
│   ├── backend/              # Backend server management
│   │   ├── mod.rs
│   │   ├── pool.rs
│   │   └── server.rs
│   └── utils/                # Utility functions
│       ├── mod.rs
│       └── logging.rs
├── tests/                    # Integration tests
├── benches/                  # Performance benchmarks
└── examples/                 # Usage examples
```

This structure follows Rust best practices:
- **Separation of Concerns**: Each module has a specific responsibility
- **Testability**: Clear boundaries make testing easier
- **Extensibility**: Easy to add new algorithms and features
- **Maintainability**: Logical organization aids development

## Configuring Dependencies

Let's set up our `Cargo.toml` with the essential dependencies:

```toml
[package]
name = "rusty-balancer"
version = "0.1.0"
edition = "2021"
authors = ["Your Name <<EMAIL>>"]
description = "A high-performance load balancer built in Rust"
license = "MIT OR Apache-2.0"
repository = "https://github.com/yourusername/rusty-balancer"

[dependencies]
# Async runtime - the foundation of our async networking
tokio = { version = "1.0", features = ["full"] }

# HTTP parsing and handling
hyper = { version = "0.14", features = ["full"] }
http = "0.2"

# Serialization for configuration
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.8"

# Logging and observability
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }

# Error handling
anyhow = "1.0"
thiserror = "1.0"

# Async utilities
futures = "0.3"
async-trait = "0.1"

# Network utilities
socket2 = "0.5"

# Configuration management
clap = { version = "4.0", features = ["derive"] }

[dev-dependencies]
# Testing utilities
tokio-test = "0.4"
criterion = { version = "0.5", features = ["html_reports"] }

[[bench]]
name = "load_balancer_bench"
harness = false

[profile.release]
# Optimize for performance in release builds
lto = true
codegen-units = 1
panic = "abort"
```

### Dependency Explanations

**Core Async Runtime**:
- `tokio`: The most popular async runtime for Rust, provides async I/O, timers, and task scheduling
- `futures`: Additional utilities for working with async code

**HTTP Handling**:
- `hyper`: Fast HTTP implementation, both client and server
- `http`: HTTP types and utilities

**Configuration and CLI**:
- `serde`: Serialization framework for configuration files
- `clap`: Command-line argument parsing
- `toml`/`serde_json`: Configuration file formats

**Error Handling**:
- `anyhow`: Flexible error handling for applications
- `thiserror`: Derive macros for custom error types

**Observability**:
- `tracing`: Structured logging and instrumentation
- `tracing-subscriber`: Log formatting and filtering

## Initial Project Structure

Let's create the basic module structure:

```bash
# Create directory structure
mkdir -p src/{config,proxy,balancer,health,backend,utils}
mkdir -p {tests,benches,examples}

# Create module files
touch src/lib.rs
touch src/config/mod.rs
touch src/proxy/mod.rs
touch src/balancer/mod.rs
touch src/health/mod.rs
touch src/backend/mod.rs
touch src/utils/mod.rs
```

### Setting Up the Library Root

Create `src/lib.rs` to establish our library's public API:

```rust
//! Rusty Balancer - A high-performance load balancer built in Rust
//!
//! This library provides the core functionality for load balancing,
//! including various algorithms, health checking, and proxy capabilities.

pub mod config;
pub mod proxy;
pub mod balancer;
pub mod health;
pub mod backend;
pub mod utils;

// Re-export commonly used types for easier imports
pub use config::Config;
pub use proxy::{TcpProxy, HttpProxy};
pub use balancer::LoadBalancer;
pub use health::HealthChecker;
pub use backend::{Backend, BackendPool};

/// Result type used throughout the library
/// Using a boxed error trait object for flexibility
pub type Result<T> = std::result::Result<T, Box<dyn std::error::Error + Send + Sync>>;
```

### Basic Main Function

Update `src/main.rs`:

```rust
use rusty_balancer::{Config, Result};
use tracing::{info, error};

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    tracing_subscriber::fmt::init();
    
    info!("Starting Rusty Balancer v{}", env!("CARGO_PKG_VERSION"));
    
    // Load configuration (we'll implement this in the next module)
    let config = Config::load().await?;
    info!("Configuration loaded successfully");
    
    // TODO: Initialize and start the load balancer
    info!("Load balancer would start here");
    
    Ok(())
}
```

## Build and Test Setup

Let's verify our setup works:

```bash
# Build the project
cargo build

# Run with logging
RUST_LOG=info cargo run

# Run tests (none yet, but good to verify)
cargo test

# Check for common issues
cargo clippy
```

You should see output like:
```
INFO rusty_balancer: Starting Rusty Balancer v0.1.0
INFO rusty_balancer: Load balancer would start here
```

## Development Workflow

Set up a productive development environment:

```bash
# Auto-rebuild and run on file changes
cargo install cargo-watch
cargo watch -x run

# In another terminal, auto-run tests
cargo watch -x test
```

## Next Steps Preview

In the upcoming modules, we'll:

1. **Module 02**: Implement a basic TCP proxy that forwards connections
2. **Module 03**: Add HTTP parsing and request/response handling
3. **Module 04**: Implement round-robin load balancing algorithm
4. **Module 05**: Add health checking for backend servers

Each module will introduce new Rust concepts while building practical load balancer functionality.

## Key Takeaways

- **Architecture Matters**: Good initial design prevents future refactoring pain
- **Async is Essential**: Load balancers are I/O-bound and benefit greatly from async programming
- **Modularity**: Clean separation of concerns makes the codebase maintainable
- **Dependencies**: Choose mature, well-maintained crates for core functionality
- **Observability**: Logging and metrics are crucial for production systems

## Navigation
- [Previous: Introduction](00-introduction.md)
- [Next: Basic TCP Proxy](02-basic-tcp-proxy.md)
