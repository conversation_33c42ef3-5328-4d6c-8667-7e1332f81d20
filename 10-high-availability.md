# Module 10: High Availability

## Learning Objectives
- Implement active-passive failover
- Add leader election using Raft consensus
- Create state replication between load balancers
- Handle network partitions and split-brain scenarios

## Prerequisites
- Completion of Module 09: Advanced Load Balancing
- Understanding of distributed systems concepts
- Familiarity with consensus algorithms
- Knowledge of failure detection mechanisms

## Navigation
- [Previous: Advanced Load Balancing](09-advanced-balancing.md)
- [Next: Traffic Management](11-traffic-management.md)

---

## High Availability Architecture

Let's implement a fault-tolerant load balancer cluster using the Raft consensus algorithm:

```rust
use raft::{Config, Node, Storage};
use tokio::sync::mpsc;
use std::time::Duration;

pub struct HaCluster {
    node_id: u64,
    peers: Vec<PeerInfo>,
    raft_node: Node,
    state: Arc<RwLock<ClusterState>>,
}

struct PeerInfo {
    id: u64,
    addr: SocketAddr,
}

#[derive(Clone, Serialize, Deserialize)]
struct ClusterState {
    active_backends: Vec<Backend>,
    session_data: HashMap<String, String>,
    config: LoadBalancerConfig,
}

impl HaCluster {
    pub async fn new(node_id: u64, peers: Vec<PeerInfo>) -> Result<Self> {
        let config = Config {
            id: node_id,
            election_tick: 10,
            heartbeat_tick: 3,
            max_size_per_msg: 1024 * 1024,
            ..Default::default()
        };

        let storage = Box::new(RaftStorage::new());
        let (tx, rx) = mpsc::channel(100);
        
        let raft_node = Node::new(config, storage, tx).await?;
        
        let state = Arc::new(RwLock::new(ClusterState::default()));
        
        Ok(Self {
            node_id,
            peers,
            raft_node,
            state,
        })
    }

    pub async fn start(&self) -> Result<()> {
        // Start Raft node
        self.raft_node.start().await?;
        
        // Connect to peers
        for peer in &self.peers {
            self.raft_node.add_peer(peer.id, peer.addr).await?;
        }
        
        // Start heartbeat
        self.start_heartbeat().await;
        
        Ok(())
    }
}
```

---

## Leader Election

Implement Raft-based leader election:

```rust
impl HaCluster {
    async fn handle_election(&mut self) {
        let mut election_timeout = tokio::time::interval(
            Duration::from_millis(rand::thread_rng().gen_range(150..300))
        );

        loop {
            election_timeout.tick().await;
            
            if !self.raft_node.is_leader() {
                // Start election
                if let Err(e) = self.raft_node.campaign().await {
                    error!("Failed to start election: {}", e);
                    continue;
                }
            }
            
            // If we became leader, initialize leader state
            if self.raft_node.is_leader() {
                self.initialize_leader_state().await;
            }
        }
    }

    async fn initialize_leader_state(&mut self) {
        info!("Node {} became leader", self.node_id);
        
        // Update cluster state
        let mut state = self.state.write().await;
        state.leader_id = self.node_id;
        state.term = self.raft_node.current_term();
        
        // Notify peers
        self.broadcast_leader_state().await;
    }
}
```

---

## State Replication

Implement state replication between nodes:

```rust
use tokio::sync::broadcast;

pub struct StateReplication {
    tx: broadcast::Sender<StateUpdate>,
    state: Arc<RwLock<ClusterState>>,
}

#[derive(Clone, Serialize, Deserialize)]
enum StateUpdate {
    ConfigChange(LoadBalancerConfig),
    BackendUpdate(Vec<Backend>),
    SessionUpdate(String, String),
}

impl StateReplication {
    pub fn new(state: Arc<RwLock<ClusterState>>) -> Self {
        let (tx, _) = broadcast::channel(100);
        Self { tx, state }
    }

    pub async fn apply_update(&self, update: StateUpdate) -> Result<()> {
        // Apply update to local state
        let mut state = self.state.write().await;
        match &update {
            StateUpdate::ConfigChange(config) => {
                state.config = config.clone();
            }
            StateUpdate::BackendUpdate(backends) => {
                state.active_backends = backends.clone();
            }
            StateUpdate::SessionUpdate(key, value) => {
                state.session_data.insert(key.clone(), value.clone());
            }
        }

        // Replicate to other nodes
        self.tx.send(update)?;
        Ok(())
    }

    pub async fn start_replication(&self) {
        let mut rx = self.tx.subscribe();
        
        tokio::spawn(async move {
            while let Ok(update) = rx.recv().await {
                if let Err(e) = self.handle_update(update).await {
                    error!("Failed to handle state update: {}", e);
                }
            }
        });
    }
}
```

---

## Split-Brain Prevention

Handle network partitions and prevent split-brain scenarios:

```rust
pub struct PartitionHandler {
    quorum_size: usize,
    peers: Vec<PeerInfo>,
    fence: Arc<AtomicBool>,
}

impl PartitionHandler {
    pub fn new(peers: Vec<PeerInfo>) -> Self {
        let quorum_size = (peers.len() / 2) + 1;
        Self {
            quorum_size,
            peers,
            fence: Arc::new(AtomicBool::new(true)),
        }
    }

    pub async fn check_quorum(&self) -> bool {
        let mut active_peers = 1; // Count self
        
        for peer in &self.peers {
            if self.check_peer_health(peer).await {
                active_peers += 1;
            }
        }
        
        let has_quorum = active_peers >= self.quorum_size;
        self.fence.store(has_quorum, Ordering::SeqCst);
        has_quorum
    }

    pub async fn handle_partition(&self) {
        if !self.fence.load(Ordering::SeqCst) {
            // Step down if we're the leader
            if self.is_leader() {
                self.step_down().await;
            }
            
            // Stop accepting requests
            self.pause_traffic().await;
        }
    }
}
```

---

## Failover Process

Implement active-passive failover:

```rust
pub struct Failover {
    virtual_ip: IpAddr,
    state: Arc<RwLock<FailoverState>>,
}

#[derive(Clone)]
enum FailoverState {
    Active,
    Passive,
    Transitioning,
}

impl Failover {
    pub async fn promote_to_active(&self) -> Result<()> {
        let mut state = self.state.write().await;
        *state = FailoverState::Transitioning;
        
        // Take over virtual IP
        self.acquire_virtual_ip().await?;
        
        // Update state
        *state = FailoverState::Active;
        info!("Promoted to active state");
        
        Ok(())
    }

    pub async fn demote_to_passive(&self) -> Result<()> {
        let mut state = self.state.write().await;
        *state = FailoverState::Transitioning;
        
        // Release virtual IP
        self.release_virtual_ip().await?;
        
        // Update state
        *state = FailoverState::Passive;
        info!("Demoted to passive state");
        
        Ok(())
    }

    async fn acquire_virtual_ip(&self) -> Result<()> {
        // Use system commands to configure virtual IP
        let output = tokio::process::Command::new("ip")
            .args(&["addr", "add", &self.virtual_ip.to_string(), "dev", "eth0"])
            .output()
            .await?;

        if !output.status.success() {
            return Err(Error::FailoverError("Failed to acquire virtual IP"));
        }
        
        Ok(())
    }
}
```

---

## Health Monitoring

Implement comprehensive health checking:

```rust
pub struct HealthMonitor {
    peers: Vec<PeerInfo>,
    check_interval: Duration,
    failure_threshold: u32,
    recovery_threshold: u32,
}

impl HealthMonitor {
    pub async fn start(&self) {
        let mut interval = tokio::time::interval(self.check_interval);
        
        loop {
            interval.tick().await;
            self.check_cluster_health().await;
        }
    }

    async fn check_cluster_health(&self) {
        let mut health_status = HashMap::new();
        
        for peer in &self.peers {
            let status = self.check_peer(&peer).await;
            health_status.insert(peer.id, status);
            
            if !status.is_healthy() {
                self.handle_peer_failure(peer).await;
            }
        }
        
        // Update metrics
        metrics::gauge!("cluster_health",
            health_status.values().filter(|s| s.is_healthy()).count() as f64);
    }

    async fn check_peer(&self, peer: &PeerInfo) -> HealthStatus {
        // Check TCP connection
        if !self.check_tcp_connection(peer).await {
            return HealthStatus::Unhealthy;
        }
        
        // Check Raft communication
        if !self.check_raft_communication(peer).await {
            return HealthStatus::Unhealthy;
        }
        
        // Check state replication
        if !self.check_state_replication(peer).await {
            return HealthStatus::Degraded;
        }
        
        HealthStatus::Healthy
    }
}
```

---

## Summary
- Implemented Raft-based leader election
- Added state replication
- Handled network partitions
- Created active-passive failover
- Added comprehensive health monitoring

---

## Next Steps
Continue to [Module 11: Traffic Management](11-traffic-management.md) to learn about advanced traffic control features.
