# Module 20: Circuit Breaking

## Learning Objectives
- Understand circuit breaker patterns
- Implement basic circuit breaker
- Add adaptive circuit breaking
- Create distributed circuit breakers
- Handle circuit breaker events

## Prerequisites
- Completion of Module 19: Rate Limiting
- Understanding of fault tolerance
- Knowledge of distributed systems
- Familiarity with state machines

## Navigation
- [Previous: Rate Limiting](19-rate-limiting.md)
- [Next: Observability](21-observability.md)

---

## Basic Circuit Breaker

Implement a basic circuit breaker:

```rust
use std::sync::atomic::{AtomicU64, AtomicUsize, Ordering};
use std::time::{Duration, Instant};
use tokio::sync::RwLock;

#[derive(Debug, Clone, Copy, PartialEq)]
pub enum CircuitState {
    Closed,
    Open,
    HalfOpen,
}

pub struct CircuitBreaker {
    state: RwLock<CircuitState>,
    failure_threshold: u64,
    success_threshold: u64,
    reset_timeout: Duration,
    last_failure: AtomicU64,
    failure_count: AtomicUsize,
    success_count: AtomicUsize,
}

impl CircuitBreaker {
    pub fn new(
        failure_threshold: u64,
        success_threshold: u64,
        reset_timeout: Duration,
    ) -> Self {
        Self {
            state: RwLock::new(CircuitState::Closed),
            failure_threshold,
            success_threshold,
            reset_timeout,
            last_failure: AtomicU64::new(0),
            failure_count: AtomicUsize::new(0),
            success_count: AtomicUsize::new(0),
        }
    }

    pub async fn allow_request(&self) -> bool {
        match *self.state.read().await {
            CircuitState::Closed => true,
            CircuitState::Open => {
                let last_failure = self.last_failure.load(Ordering::SeqCst);
                let now = Instant::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap()
                    .as_secs();
                    
                if now - last_failure >= self.reset_timeout.as_secs() {
                    // Transition to half-open
                    *self.state.write().await = CircuitState::HalfOpen;
                    true
                } else {
                    false
                }
            }
            CircuitState::HalfOpen => {
                // Allow limited traffic
                self.success_count.load(Ordering::SeqCst)
                    < self.success_threshold as usize
            }
        }
    }

    pub async fn on_success(&self) {
        match *self.state.read().await {
            CircuitState::Closed => {
                // Reset failure count
                self.failure_count.store(0, Ordering::SeqCst);
            }
            CircuitState::HalfOpen => {
                let success = self.success_count
                    .fetch_add(1, Ordering::SeqCst)
                    + 1;
                    
                if success >= self.success_threshold as usize {
                    // Return to closed state
                    *self.state.write().await = CircuitState::Closed;
                    self.success_count.store(0, Ordering::SeqCst);
                    self.failure_count.store(0, Ordering::SeqCst);
                }
            }
            CircuitState::Open => {}
        }
    }

    pub async fn on_failure(&self) {
        match *self.state.read().await {
            CircuitState::Closed => {
                let failures = self.failure_count
                    .fetch_add(1, Ordering::SeqCst)
                    + 1;
                    
                if failures >= self.failure_threshold as usize {
                    // Open the circuit
                    *self.state.write().await = CircuitState::Open;
                    self.last_failure.store(
                        Instant::now()
                            .duration_since(std::time::UNIX_EPOCH)
                            .unwrap()
                            .as_secs(),
                        Ordering::SeqCst,
                    );
                }
            }
            CircuitState::HalfOpen => {
                // Return to open state
                *self.state.write().await = CircuitState::Open;
                self.last_failure.store(
                    Instant::now()
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap()
                        .as_secs(),
                    Ordering::SeqCst,
                );
                self.success_count.store(0, Ordering::SeqCst);
            }
            CircuitState::Open => {
                self.last_failure.store(
                    Instant::now()
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap()
                        .as_secs(),
                    Ordering::SeqCst,
                );
            }
        }
    }
}
```

---

## Adaptive Circuit Breaker

Implement an adaptive circuit breaker:

```rust
use std::sync::atomic::{AtomicU64, Ordering};
use std::time::{Duration, Instant};
use exponential_backoff::backoff::Backoff;
use metrics_runtime::Recorder;

pub struct AdaptiveCircuitBreaker {
    base: CircuitBreaker,
    error_rate_threshold: f64,
    sample_size: usize,
    window_size: Duration,
    metrics: Arc<dyn Recorder>,
    backoff: Backoff,
}

impl AdaptiveCircuitBreaker {
    pub fn new(
        error_rate_threshold: f64,
        sample_size: usize,
        window_size: Duration,
        metrics: Arc<dyn Recorder>,
    ) -> Self {
        Self {
            base: CircuitBreaker::new(
                sample_size as u64,
                sample_size as u64,
                window_size,
            ),
            error_rate_threshold,
            sample_size,
            window_size,
            metrics,
            backoff: Backoff::default(),
        }
    }

    pub async fn allow_request(&self) -> bool {
        if !self.base.allow_request().await {
            return false;
        }
        
        // Check error rate
        let error_rate = self.calculate_error_rate().await;
        error_rate <= self.error_rate_threshold
    }

    async fn calculate_error_rate(&self) -> f64 {
        let now = Instant::now();
        let window_start = now - self.window_size;
        
        let mut success_count = 0;
        let mut error_count = 0;
        
        // Get metrics from window
        let metrics = self.metrics
            .get_counter("circuit_breaker_requests")
            .unwrap();
            
        for (timestamp, value) in metrics.iter() {
            if timestamp >= window_start {
                match value {
                    "success" => success_count += 1,
                    "error" => error_count += 1,
                    _ => {}
                }
            }
        }
        
        let total = success_count + error_count;
        if total >= self.sample_size {
            error_count as f64 / total as f64
        } else {
            0.0
        }
    }

    pub async fn on_success(&self) {
        self.base.on_success().await;
        self.metrics
            .counter("circuit_breaker_requests")
            .unwrap()
            .record(1, "success");
            
        // Reset backoff
        self.backoff.reset();
    }

    pub async fn on_failure(&self) {
        self.base.on_failure().await;
        self.metrics
            .counter("circuit_breaker_requests")
            .unwrap()
            .record(1, "error");
            
        // Increase backoff
        self.backoff.next_backoff();
    }

    pub fn get_backoff_duration(&self) -> Duration {
        self.backoff.get_current_duration()
    }
}
```

---

## Distributed Circuit Breaker

Implement a distributed circuit breaker using Redis:

```rust
use redis::{Commands, Connection, RedisResult};
use std::time::{Duration, SystemTime, UNIX_EPOCH};

pub struct DistributedCircuitBreaker {
    redis: Connection,
    key_prefix: String,
    failure_threshold: u64,
    success_threshold: u64,
    reset_timeout: Duration,
}

impl DistributedCircuitBreaker {
    pub fn new(
        redis_url: &str,
        key_prefix: &str,
        failure_threshold: u64,
        success_threshold: u64,
        reset_timeout: Duration,
    ) -> RedisResult<Self> {
        let client = redis::Client::open(redis_url)?;
        let redis = client.get_connection()?;
        
        Ok(Self {
            redis,
            key_prefix: key_prefix.to_string(),
            failure_threshold,
            success_threshold,
            reset_timeout,
        })
    }

    pub async fn allow_request(
        &mut self,
        service: &str,
    ) -> RedisResult<bool> {
        let state_key = format!("{}:{}:state", self.key_prefix, service);
        let state: Option<String> = self.redis.get(&state_key)?;
        
        match state.as_deref() {
            Some("CLOSED") => Ok(true),
            Some("OPEN") => {
                let last_failure_key = format!(
                    "{}:{}:last_failure",
                    self.key_prefix,
                    service,
                );
                let last_failure: Option<u64> = self.redis
                    .get(&last_failure_key)?;
                    
                if let Some(last_failure) = last_failure {
                    let now = SystemTime::now()
                        .duration_since(UNIX_EPOCH)
                        .unwrap()
                        .as_secs();
                        
                    if now - last_failure
                        >= self.reset_timeout.as_secs()
                    {
                        // Transition to half-open
                        self.redis
                            .set(&state_key, "HALF_OPEN")?;
                        Ok(true)
                    } else {
                        Ok(false)
                    }
                } else {
                    Ok(false)
                }
            }
            Some("HALF_OPEN") => {
                let success_key = format!(
                    "{}:{}:success_count",
                    self.key_prefix,
                    service,
                );
                let success_count: u64 = self.redis
                    .get(&success_key)
                    .unwrap_or(0);
                    
                Ok(success_count < self.success_threshold)
            }
            _ => {
                // Initialize closed state
                self.redis.set(&state_key, "CLOSED")?;
                Ok(true)
            }
        }
    }

    pub async fn on_success(
        &mut self,
        service: &str,
    ) -> RedisResult<()> {
        let state_key = format!("{}:{}:state", self.key_prefix, service);
        let state: Option<String> = self.redis.get(&state_key)?;
        
        match state.as_deref() {
            Some("CLOSED") => {
                // Reset failure count
                let failure_key = format!(
                    "{}:{}:failure_count",
                    self.key_prefix,
                    service,
                );
                self.redis.set(&failure_key, 0)?;
            }
            Some("HALF_OPEN") => {
                let success_key = format!(
                    "{}:{}:success_count",
                    self.key_prefix,
                    service,
                );
                let success_count: u64 = self.redis
                    .incr(&success_key, 1)?;
                    
                if success_count >= self.success_threshold {
                    // Return to closed state
                    self.redis
                        .set(&state_key, "CLOSED")?;
                    self.redis
                        .del(&success_key)?;
                }
            }
            _ => {}
        }
        
        Ok(())
    }

    pub async fn on_failure(
        &mut self,
        service: &str,
    ) -> RedisResult<()> {
        let state_key = format!("{}:{}:state", self.key_prefix, service);
        let state: Option<String> = self.redis.get(&state_key)?;
        
        match state.as_deref() {
            Some("CLOSED") => {
                let failure_key = format!(
                    "{}:{}:failure_count",
                    self.key_prefix,
                    service,
                );
                let failures: u64 = self.redis
                    .incr(&failure_key, 1)?;
                    
                if failures >= self.failure_threshold {
                    // Open the circuit
                    self.redis
                        .set(&state_key, "OPEN")?;
                    let now = SystemTime::now()
                        .duration_since(UNIX_EPOCH)
                        .unwrap()
                        .as_secs();
                    let last_failure_key = format!(
                        "{}:{}:last_failure",
                        self.key_prefix,
                        service,
                    );
                    self.redis
                        .set(&last_failure_key, now)?;
                }
            }
            Some("HALF_OPEN") => {
                // Return to open state
                self.redis
                    .set(&state_key, "OPEN")?;
                let now = SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs();
                let last_failure_key = format!(
                    "{}:{}:last_failure",
                    self.key_prefix,
                    service,
                );
                self.redis
                    .set(&last_failure_key, now)?;
            }
            _ => {}
        }
        
        Ok(())
    }
}
```

---

## Circuit Breaker Middleware

Create a circuit breaker middleware:

```rust
use std::future::Future;
use std::pin::Pin;
use std::sync::Arc;
use std::task::{Context, Poll};
use hyper::{Body, Request, Response, StatusCode};
use tower::{Layer, Service};

pub struct CircuitBreakerLayer<B> {
    breaker: Arc<B>,
}

impl<B> CircuitBreakerLayer<B> {
    pub fn new(breaker: B) -> Self {
        Self {
            breaker: Arc::new(breaker),
        }
    }
}

impl<S, B> Layer<S> for CircuitBreakerLayer<B>
where
    B: CircuitBreaker,
{
    type Service = CircuitBreakerService<S, B>;

    fn layer(&self, service: S) -> Self::Service {
        CircuitBreakerService {
            inner: service,
            breaker: self.breaker.clone(),
        }
    }
}

pub struct CircuitBreakerService<S, B> {
    inner: S,
    breaker: Arc<B>,
}

impl<S, B> Service<Request<Body>> for CircuitBreakerService<S, B>
where
    S: Service<Request<Body>, Response = Response<Body>>,
    B: CircuitBreaker,
{
    type Response = S::Response;
    type Error = S::Error;
    type Future = Pin<Box<dyn Future<
        Output = Result<Self::Response, Self::Error>,
    > + Send>>;

    fn poll_ready(
        &mut self,
        cx: &mut Context<'_>,
    ) -> Poll<Result<(), Self::Error>> {
        self.inner.poll_ready(cx)
    }

    fn call(&mut self, request: Request<Body>) -> Self::Future {
        let breaker = self.breaker.clone();
        let mut inner = self.inner.clone();
        
        Box::pin(async move {
            if !breaker.allow_request().await {
                // Circuit is open
                return Ok(Response::builder()
                    .status(StatusCode::SERVICE_UNAVAILABLE)
                    .body(Body::from("Circuit breaker is open"))
                    .unwrap());
            }
            
            // Forward request
            match inner.call(request).await {
                Ok(response) => {
                    if response.status().is_success() {
                        breaker.on_success().await;
                    } else {
                        breaker.on_failure().await;
                    }
                    Ok(response)
                }
                Err(err) => {
                    breaker.on_failure().await;
                    Err(err)
                }
            }
        })
    }
}
```

---

## Summary
- Implemented basic circuit breaker
- Added adaptive circuit breaking
- Created distributed circuit breaker
- Added circuit breaker middleware
- Implemented backoff strategies

---

## Next Steps
Continue to [Module 21: Observability](21-observability.md) to learn about implementing observability features.
