# Module 15: Advanced Security

## Learning Objectives
- Implement advanced TLS features
- Add mutual TLS authentication
- Create JWT validation
- Implement WAF capabilities
- Add DDoS mitigation strategies

## Prerequisites
- Completion of Module 14: Performance Optimization
- Understanding of cryptography and TLS
- Familiarity with security best practices
- Knowledge of authentication mechanisms

## Navigation
- [Previous: Performance Optimization](14-performance-optimization.md)
- [Next: API Gateway Features](16-api-gateway.md)

---

## Advanced TLS Configuration

Implement enhanced TLS security:

```rust
use rustls::{ServerConfig, ClientConfig};
use rustls::server::{ServerSessionMemoryCache, NoClientAuth};
use rustls::client::{ClientSessionMemoryCache};
use std::sync::Arc;

pub struct TlsManager {
    server_config: Arc<ServerConfig>,
    client_config: Arc<ClientConfig>,
}

impl TlsManager {
    pub fn new(
        cert_path: &Path,
        key_path: &Path,
        trusted_cas: &Path,
    ) -> Result<Self> {
        let server_config = Self::create_server_config(
            cert_path,
            key_path,
        )?;
        
        let client_config = Self::create_client_config(
            trusted_cas,
        )?;
        
        Ok(Self {
            server_config: Arc::new(server_config),
            client_config: Arc::new(client_config),
        })
    }

    fn create_server_config(
        cert_path: &Path,
        key_path: &Path,
    ) -> Result<ServerConfig> {
        let cert_chain = Self::load_certificates(cert_path)?;
        let private_key = Self::load_private_key(key_path)?;
        
        let mut config = ServerConfig::builder()
            .with_safe_default_cipher_suites()
            .with_safe_default_kx_groups()
            .with_safe_default_protocol_versions()?
            .with_no_client_auth()
            .with_single_cert(cert_chain, private_key)?;
            
        // Enable session resumption
        config.session_storage = ServerSessionMemoryCache::new(256);
        
        // Set TLS options
        config.alpn_protocols = vec![b"h2".to_vec(), b"http/1.1".to_vec()];
        
        Ok(config)
    }
}
```

---

## Mutual TLS Authentication

Implement client certificate validation:

```rust
use rustls::server::AllowAnyAuthenticatedClient;
use rustls::RootCertStore;

pub struct MutualTls {
    config: ServerConfig,
    client_cas: RootCertStore,
}

impl MutualTls {
    pub fn new(
        server_cert: &Path,
        server_key: &Path,
        client_cas: &Path,
    ) -> Result<Self> {
        let mut client_cas = RootCertStore::empty();
        
        // Load client CA certificates
        let ca_certs = Self::load_certificates(client_cas)?;
        for cert in ca_certs {
            client_cas.add(&cert)?;
        }
        
        // Create verifier
        let client_auth = AllowAnyAuthenticatedClient::new(client_cas.clone());
        
        // Create config with client authentication
        let mut config = ServerConfig::builder()
            .with_safe_defaults()
            .with_client_cert_verifier(client_auth)
            .with_single_cert(
                Self::load_certificates(server_cert)?,
                Self::load_private_key(server_key)?,
            )?;
            
        Ok(Self { config, client_cas })
    }

    pub fn verify_client_cert(
        &self,
        cert_chain: &[Certificate],
    ) -> Result<()> {
        let (end_entity, intermediates) = cert_chain
            .split_first()
            .ok_or(Error::InvalidCertChain)?;
            
        let now = SystemTime::now();
        
        // Verify certificate chain
        self.client_cas.verify_client_cert(
            end_entity,
            intermediates,
            now,
        )?;
        
        Ok(())
    }
}
```

---

## JWT Validation

Implement JWT authentication and validation:

```rust
use jsonwebtoken::{decode, encode, DecodingKey, EncodingKey, Header, Validation};
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize)]
struct Claims {
    sub: String,
    exp: usize,
    iss: String,
    aud: String,
}

pub struct JwtValidator {
    encoding_key: EncodingKey,
    decoding_key: DecodingKey<'static>,
    validation: Validation,
}

impl JwtValidator {
    pub fn new(
        secret: &[u8],
        issuer: String,
        audience: String,
    ) -> Self {
        let mut validation = Validation::new(jsonwebtoken::Algorithm::HS256);
        validation.set_issuer(&[issuer]);
        validation.set_audience(&[audience]);
        
        Self {
            encoding_key: EncodingKey::from_secret(secret),
            decoding_key: DecodingKey::from_secret(secret).into_static(),
            validation,
        }
    }

    pub fn validate_token(&self, token: &str) -> Result<Claims> {
        let token_data = decode::<Claims>(
            token,
            &self.decoding_key,
            &self.validation,
        )?;
        
        Ok(token_data.claims)
    }

    pub fn create_token(&self, sub: &str, exp: usize) -> Result<String> {
        let claims = Claims {
            sub: sub.to_string(),
            exp,
            iss: self.validation.iss.as_ref().unwrap()[0].clone(),
            aud: self.validation.aud.as_ref().unwrap()[0].clone(),
        };
        
        let token = encode(
            &Header::default(),
            &claims,
            &self.encoding_key,
        )?;
        
        Ok(token)
    }
}
```

---

## Web Application Firewall

Implement basic WAF functionality:

```rust
use regex::RegexSet;
use std::collections::HashMap;

pub struct WebAppFirewall {
    rules: Vec<WafRule>,
    ip_blacklist: HashSet<IpAddr>,
    rate_limiter: RateLimiter,
}

struct WafRule {
    patterns: RegexSet,
    action: WafAction,
    scope: WafScope,
}

enum WafAction {
    Block,
    Log,
    Alert,
}

enum WafScope {
    Headers,
    QueryString,
    Body,
    Path,
}

impl WebAppFirewall {
    pub fn new() -> Self {
        Self {
            rules: Vec::new(),
            ip_blacklist: HashSet::new(),
            rate_limiter: RateLimiter::new(100, 60), // 100 requests per minute
        }
    }

    pub async fn check_request(
        &self,
        request: &Request<Body>,
    ) -> Result<(), WafError> {
        // Check IP blacklist
        let client_ip = request
            .extensions()
            .get::<SocketAddr>()
            .ok_or(WafError::NoClientIp)?
            .ip();
            
        if self.ip_blacklist.contains(&client_ip) {
            return Err(WafError::IpBlocked);
        }
        
        // Check rate limit
        if !self.rate_limiter.check(client_ip).await {
            return Err(WafError::RateLimitExceeded);
        }
        
        // Apply WAF rules
        for rule in &self.rules {
            self.check_rule(rule, request)?;
        }
        
        Ok(())
    }

    fn check_rule(
        &self,
        rule: &WafRule,
        request: &Request<Body>,
    ) -> Result<(), WafError> {
        let content = match rule.scope {
            WafScope::Headers => self.extract_headers(request),
            WafScope::QueryString => self.extract_query(request),
            WafScope::Body => self.extract_body(request),
            WafScope::Path => self.extract_path(request),
        };
        
        if rule.patterns.is_match(&content) {
            match rule.action {
                WafAction::Block => return Err(WafError::RuleViolation),
                WafAction::Log => {
                    warn!("WAF rule violation detected: {:?}", content);
                }
                WafAction::Alert => {
                    self.send_alert(&content).await;
                }
            }
        }
        
        Ok(())
    }
}
```

---

## DDoS Mitigation

Implement advanced DDoS protection:

```rust
use tokio::sync::RwLock;
use std::collections::HashMap;
use std::net::IpAddr;

pub struct DdosProtection {
    strategies: Vec<Box<dyn MitigationStrategy>>,
    state: Arc<RwLock<DdosState>>,
}

struct DdosState {
    banned_ips: HashSet<IpAddr>,
    connection_counts: HashMap<IpAddr, usize>,
    request_rates: HashMap<IpAddr, VecDeque<Instant>>,
}

#[async_trait]
trait MitigationStrategy: Send + Sync {
    async fn check_request(
        &self,
        request: &Request<Body>,
        state: &Arc<RwLock<DdosState>>,
    ) -> Result<(), DdosError>;
}

struct ConnectionLimiter {
    max_connections: usize,
}

#[async_trait]
impl MitigationStrategy for ConnectionLimiter {
    async fn check_request(
        &self,
        request: &Request<Body>,
        state: &Arc<RwLock<DdosState>>,
    ) -> Result<(), DdosError> {
        let client_ip = request
            .extensions()
            .get::<SocketAddr>()
            .ok_or(DdosError::NoClientIp)?
            .ip();
            
        let mut state = state.write().await;
        
        let count = state
            .connection_counts
            .entry(client_ip)
            .or_insert(0);
            
        if *count >= self.max_connections {
            return Err(DdosError::TooManyConnections);
        }
        
        *count += 1;
        Ok(())
    }
}

struct RateLimiter {
    requests_per_second: usize,
    window_size: Duration,
}

#[async_trait]
impl MitigationStrategy for RateLimiter {
    async fn check_request(
        &self,
        request: &Request<Body>,
        state: &Arc<RwLock<DdosState>>,
    ) -> Result<(), DdosError> {
        let client_ip = request
            .extensions()
            .get::<SocketAddr>()
            .ok_or(DdosError::NoClientIp)?
            .ip();
            
        let mut state = state.write().await;
        
        let times = state
            .request_rates
            .entry(client_ip)
            .or_insert_with(VecDeque::new);
            
        // Remove old timestamps
        let cutoff = Instant::now() - self.window_size;
        while times.front()
            .map(|t| *t < cutoff)
            .unwrap_or(false)
        {
            times.pop_front();
        }
        
        if times.len() >= self.requests_per_second {
            return Err(DdosError::RateLimitExceeded);
        }
        
        times.push_back(Instant::now());
        Ok(())
    }
}
```

---

## Summary
- Implemented advanced TLS configuration
- Added mutual TLS authentication
- Created JWT validation
- Implemented WAF capabilities
- Added DDoS protection strategies

---

## Next Steps
Continue to [Module 16: API Gateway Features](16-api-gateway.md) to learn about implementing API gateway functionality.
