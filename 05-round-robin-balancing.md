# 03 - Load Balancing Strategies

## Goal
Introduce multiple backend servers and implement basic load balancing algorithms.

## Concepts Introduced
- Vectors and collections
- Enums and pattern matching
- Ownership and borrowing

## Why These Strategies?
We'll start with round-robin and random selection. These are simple, effective, and easy to implement. More advanced strategies (least connections, weighted, etc.) will come later.

## Alternatives Considered
- Implementing all strategies at once (overwhelming)
- Using external crates (we'll stick to std for now)

## Next Steps
We'll add configuration, error handling, and logging.
