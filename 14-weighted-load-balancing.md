# 11 - Hot Reloading of Configuration

## Goal
Enable the load balancer to reload its configuration at runtime without downtime.

## Concepts Introduced
- File watching (e.g., using `notify` crate)
- Atomic updates and shared state
- Graceful reconfiguration

## Why Hot Reloading?
It allows changes to backend lists, ports, or strategies without restarting the service, improving uptime and flexibility.

## Alternatives Considered
- Manual restarts (causes downtime)
- API-based reconfiguration (more complex, covered later)

## Next Steps
We'll add dynamic backend discovery for even more flexibility.
