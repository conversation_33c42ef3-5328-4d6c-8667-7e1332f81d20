# Module 04: Round-Robin Load Balancing

## Learning Objectives
- Understand load balancing algorithms and their trade-offs
- Implement round-robin algorithm with thread-safe state management
- Learn Rust's concurrency primitives: Arc, Mutex, and atomic operations
- Build a backend server pool with dynamic server management
- Implement request distribution across multiple backend servers
- Handle backend server failures gracefully

## Prerequisites
- Completion of Module 03: HTTP Protocol Implementation
- Understanding of concurrent programming concepts
- Familiarity with <PERSON><PERSON>'s ownership model and smart pointers
- Knowledge of atomic operations and thread safety

## Navigation
- [Previous: HTTP Protocol Implementation](03-http-protocol.md)
- [Next: Health Checking System](05-health-checking.md)

## Load Balancing Algorithms Overview

Load balancing algorithms determine how requests are distributed across backend servers. Each algorithm has different characteristics and use cases.

### Common Algorithms

```mermaid
flowchart TD
    subgraph Algorithms[Load Balancing Algorithms]
        RR[Round Robin<br/>Simple, Fair Distribution]
        WRR[Weighted Round Robin<br/>Capacity-Based Distribution]
        LC[Least Connections<br/>Load-Based Distribution]
        CH[Consistent Hashing<br/>Session Affinity]
        RND[Random<br/>Simple, No State]
    end
    
    subgraph Characteristics[Key Characteristics]
        S[Simplicity]
        F[Fairness]
        P[Performance]
        A[Adaptability]
    end
    
    RR --> S
    WRR --> F
    LC --> A
    CH --> P
    RND --> S
```

### Round-Robin Algorithm

**Advantages**:
- Simple to implement and understand
- Fair distribution when servers have equal capacity
- No complex state tracking required
- Predictable behavior

**Disadvantages**:
- Doesn't consider server load or capacity
- May not work well with long-running requests
- Assumes all servers are equally capable

**Use Cases**:
- Homogeneous server environments
- Short-lived requests (web pages, API calls)
- Development and testing environments

## Rust Concurrency Concepts

### Thread Safety in Load Balancers

Load balancers must handle concurrent requests safely:

```rust
use std::sync::{Arc, Mutex};
use std::sync::atomic::{AtomicUsize, Ordering};

// Shared state between threads
struct SharedState {
    counter: AtomicUsize,           // Lock-free counter
    servers: Mutex<Vec<Server>>,    // Protected server list
}

// Arc enables shared ownership
let state = Arc::new(SharedState::new());
```

### Atomic Operations vs Mutexes

**Atomic Operations**:
- Lock-free, high performance
- Limited to simple operations
- Perfect for counters and flags

**Mutexes**:
- Can protect complex data structures
- Potential for contention and blocking
- Necessary for non-atomic operations

## Implementation: Backend Server Pool

Let's start by implementing a backend server representation and pool management.

### Backend Server Model

Create `src/backend/mod.rs`:

```rust
//! Backend server management and pooling
//! 
//! This module handles backend server representation, health tracking,
//! and connection pooling for efficient request distribution.

pub mod server;
pub mod pool;

pub use server::{Backend, BackendStatus};
pub use pool::BackendPool;

use std::net::SocketAddr;
use std::time::{Duration, Instant};

/// Represents a backend server with health and performance metrics
#[derive(Debug, Clone)]
pub struct Backend {
    /// Server address
    pub addr: SocketAddr,
    /// Current health status
    pub status: BackendStatus,
    /// Weight for weighted algorithms (1-100)
    pub weight: u32,
    /// Current active connections
    pub active_connections: u32,
    /// Last health check timestamp
    pub last_health_check: Option<Instant>,
    /// Response time moving average (milliseconds)
    pub avg_response_time: Duration,
}

/// Backend server health status
#[derive(Debug, Clone, PartialEq)]
pub enum BackendStatus {
    /// Server is healthy and accepting requests
    Healthy,
    /// Server is unhealthy, don't send requests
    Unhealthy,
    /// Server is being drained, finish existing requests only
    Draining,
    /// Server is disabled by administrator
    Disabled,
}

impl Backend {
    /// Create a new backend server
    pub fn new(addr: SocketAddr) -> Self {
        Self {
            addr,
            status: BackendStatus::Healthy,
            weight: 1,
            active_connections: 0,
            last_health_check: None,
            avg_response_time: Duration::from_millis(0),
        }
    }
    
    /// Check if server can accept new requests
    pub fn is_available(&self) -> bool {
        matches!(self.status, BackendStatus::Healthy)
    }
    
    /// Update server metrics after request completion
    pub fn update_metrics(&mut self, response_time: Duration) {
        // Simple moving average (in production, use exponential moving average)
        self.avg_response_time = Duration::from_millis(
            (self.avg_response_time.as_millis() as u64 + response_time.as_millis() as u64) / 2
        );
    }
}
```

### Backend Pool Implementation

Create `src/backend/pool.rs`:

```rust
use super::{Backend, BackendStatus};
use std::net::SocketAddr;
use std::sync::{Arc, Mutex};
use std::sync::atomic::{AtomicUsize, Ordering};
use tracing::{info, warn, debug};

/// Thread-safe pool of backend servers with load balancing
pub struct BackendPool {
    /// List of backend servers
    servers: Arc<Mutex<Vec<Backend>>>,
    /// Round-robin counter (atomic for lock-free access)
    round_robin_counter: AtomicUsize,
}

impl BackendPool {
    /// Create a new backend pool
    pub fn new() -> Self {
        Self {
            servers: Arc::new(Mutex::new(Vec::new())),
            round_robin_counter: AtomicUsize::new(0),
        }
    }
    
    /// Add a backend server to the pool
    pub fn add_backend(&self, addr: SocketAddr) -> crate::Result<()> {
        let mut servers = self.servers.lock().unwrap();
        
        // Check if server already exists
        if servers.iter().any(|s| s.addr == addr) {
            warn!("Backend {} already exists in pool", addr);
            return Ok(());
        }
        
        let backend = Backend::new(addr);
        servers.push(backend);
        info!("Added backend {} to pool (total: {})", addr, servers.len());
        
        Ok(())
    }
    
    /// Remove a backend server from the pool
    pub fn remove_backend(&self, addr: SocketAddr) -> crate::Result<()> {
        let mut servers = self.servers.lock().unwrap();
        
        if let Some(pos) = servers.iter().position(|s| s.addr == addr) {
            servers.remove(pos);
            info!("Removed backend {} from pool (remaining: {})", addr, servers.len());
        } else {
            warn!("Backend {} not found in pool", addr);
        }
        
        Ok(())
    }
    
    /// Get next backend using round-robin algorithm
    pub fn get_next_backend(&self) -> Option<SocketAddr> {
        let servers = self.servers.lock().unwrap();
        
        if servers.is_empty() {
            warn!("No backends available in pool");
            return None;
        }
        
        // Filter healthy servers
        let healthy_servers: Vec<&Backend> = servers
            .iter()
            .filter(|s| s.is_available())
            .collect();
        
        if healthy_servers.is_empty() {
            warn!("No healthy backends available");
            return None;
        }
        
        // Get next server using round-robin
        let counter = self.round_robin_counter.fetch_add(1, Ordering::Relaxed);
        let index = counter % healthy_servers.len();
        let selected = healthy_servers[index];
        
        debug!("Selected backend {} (index: {}/{})", 
               selected.addr, index, healthy_servers.len());
        
        Some(selected.addr)
    }
    
    /// Get all backend servers (for monitoring/admin)
    pub fn get_all_backends(&self) -> Vec<Backend> {
        let servers = self.servers.lock().unwrap();
        servers.clone()
    }
    
    /// Update backend status
    pub fn update_backend_status(&self, addr: SocketAddr, status: BackendStatus) {
        let mut servers = self.servers.lock().unwrap();
        
        if let Some(backend) = servers.iter_mut().find(|s| s.addr == addr) {
            let old_status = backend.status.clone();
            backend.status = status;
            info!("Backend {} status changed: {:?} -> {:?}", 
                  addr, old_status, backend.status);
        }
    }
    
    /// Get pool statistics
    pub fn get_stats(&self) -> PoolStats {
        let servers = self.servers.lock().unwrap();
        
        let total = servers.len();
        let healthy = servers.iter().filter(|s| s.is_available()).count();
        let unhealthy = servers.iter().filter(|s| !s.is_available()).count();
        
        PoolStats {
            total_servers: total,
            healthy_servers: healthy,
            unhealthy_servers: unhealthy,
            requests_served: self.round_robin_counter.load(Ordering::Relaxed),
        }
    }
}

/// Backend pool statistics
#[derive(Debug, Clone)]
pub struct PoolStats {
    pub total_servers: usize,
    pub healthy_servers: usize,
    pub unhealthy_servers: usize,
    pub requests_served: usize,
}

impl Default for BackendPool {
    fn default() -> Self {
        Self::new()
    }
}

// Thread safety: BackendPool can be safely shared between threads
unsafe impl Send for BackendPool {}
unsafe impl Sync for BackendPool {}
```

## Enhanced HTTP Proxy with Load Balancing

Now let's update our HTTP proxy to use the backend pool:

```rust
// Update src/proxy/http_proxy.rs
use crate::backend::BackendPool;
use hyper::{Body, Request, Response, StatusCode};
use hyper::client::HttpConnector;
use hyper::service::{make_service_fn, service_fn};
use hyper::{Client, Server};
use std::convert::Infallible;
use std::net::SocketAddr;
use std::sync::Arc;
use tracing::{info, warn, error, debug};

/// HTTP proxy with load balancing capabilities
pub struct HttpProxy {
    backend_pool: Arc<BackendPool>,
    client: Client<HttpConnector>,
}

impl HttpProxy {
    /// Create a new HTTP proxy with backend pool
    pub fn new(backend_pool: Arc<BackendPool>) -> Self {
        let client = Client::new();
        Self {
            backend_pool,
            client,
        }
    }
    
    /// Add a backend server to the pool
    pub fn add_backend(&self, addr: SocketAddr) -> crate::Result<()> {
        self.backend_pool.add_backend(addr)
    }
    
    /// Start the HTTP proxy server
    pub async fn start(&self, bind_addr: SocketAddr) -> crate::Result<()> {
        let backend_pool = Arc::clone(&self.backend_pool);
        let client = self.client.clone();
        
        let make_svc = make_service_fn(move |_conn| {
            let backend_pool = Arc::clone(&backend_pool);
            let client = client.clone();
            
            async move {
                Ok::<_, Infallible>(service_fn(move |req| {
                    Self::handle_request(req, client.clone(), Arc::clone(&backend_pool))
                }))
            }
        });
        
        let server = Server::bind(&bind_addr).serve(make_svc);
        info!("HTTP Load Balancer listening on {}", bind_addr);
        
        // Print initial backend pool stats
        let stats = backend_pool.get_stats();
        info!("Backend pool initialized: {} servers ({} healthy)", 
              stats.total_servers, stats.healthy_servers);
        
        if let Err(e) = server.await {
            error!("HTTP proxy server error: {}", e);
        }
        
        Ok(())
    }
    
    /// Handle HTTP request with load balancing
    async fn handle_request(
        mut req: Request<Body>,
        client: Client<HttpConnector>,
        backend_pool: Arc<BackendPool>,
    ) -> Result<Response<Body>, Infallible> {
        debug!("Handling request: {} {}", req.method(), req.uri());
        
        // Select backend using load balancing
        let backend_addr = match backend_pool.get_next_backend() {
            Some(addr) => addr,
            None => {
                error!("No healthy backends available");
                return Ok(Self::error_response(StatusCode::SERVICE_UNAVAILABLE));
            }
        };
        
        debug!("Selected backend: {}", backend_addr);
        
        // Prepare request for backend
        if let Err(e) = Self::prepare_backend_request(&mut req, backend_addr) {
            error!("Failed to prepare backend request: {}", e);
            return Ok(Self::error_response(StatusCode::BAD_GATEWAY));
        }
        
        // Forward request to selected backend
        match client.request(req).await {
            Ok(mut response) => {
                debug!("Backend {} responded with: {}", backend_addr, response.status());
                Self::prepare_client_response(&mut response);
                Ok(response)
            }
            Err(e) => {
                error!("Backend {} request failed: {}", backend_addr, e);
                // TODO: Mark backend as unhealthy
                Ok(Self::error_response(StatusCode::BAD_GATEWAY))
            }
        }
    }
    
    // ... (rest of the methods remain the same as Module 03)
}
```

## Testing the Load Balancer

Let's create a comprehensive test setup:

```rust
// Update src/main.rs
use rusty_balancer::proxy::HttpProxy;
use rusty_balancer::backend::BackendPool;
use std::net::SocketAddr;
use std::sync::Arc;
use tracing::info;

#[tokio::main]
async fn main() -> rusty_balancer::Result<()> {
    tracing_subscriber::fmt::init();
    
    info!("Starting Rusty Load Balancer");
    
    // Create backend pool
    let backend_pool = Arc::new(BackendPool::new());
    
    // Add multiple backend servers
    let backends = vec![
        "127.0.0.1:8081".parse::<SocketAddr>()?,
        "127.0.0.1:8082".parse::<SocketAddr>()?,
        "127.0.0.1:8083".parse::<SocketAddr>()?,
    ];
    
    for backend in backends {
        backend_pool.add_backend(backend)?;
    }
    
    // Create and start proxy
    let bind_addr: SocketAddr = "127.0.0.1:8080".parse()?;
    let proxy = HttpProxy::new(backend_pool);
    
    proxy.start(bind_addr).await?;
    
    Ok(())
}
```

### Testing Setup

1. **Start multiple backend servers**:
```bash
# Terminal 1
python3 -m http.server 8081

# Terminal 2  
python3 -m http.server 8082

# Terminal 3
python3 -m http.server 8083
```

2. **Start the load balancer**:
```bash
RUST_LOG=debug cargo run
```

3. **Test load distribution**:
```bash
# Send multiple requests and observe distribution
for i in {1..10}; do
    curl -s http://127.0.0.1:8080/ | head -1
done
```

## Performance Considerations

### Atomic Operations Performance

```rust
// Relaxed ordering is sufficient for counters
let counter = self.round_robin_counter.fetch_add(1, Ordering::Relaxed);

// For critical sections, use stronger ordering
let counter = self.round_robin_counter.load(Ordering::Acquire);
```

### Lock Contention Minimization

```rust
// Keep critical sections short
{
    let servers = self.servers.lock().unwrap();
    // Do minimal work here
    let healthy_count = servers.iter().filter(|s| s.is_available()).count();
} // Lock released here

// Do expensive work outside the lock
process_server_data(healthy_count);
```

## Advanced Round-Robin Variations

### Weighted Round-Robin

```rust
impl BackendPool {
    pub fn get_next_weighted_backend(&self) -> Option<SocketAddr> {
        let servers = self.servers.lock().unwrap();
        
        // Calculate total weight
        let total_weight: u32 = servers.iter()
            .filter(|s| s.is_available())
            .map(|s| s.weight)
            .sum();
        
        if total_weight == 0 {
            return None;
        }
        
        // Select based on weight
        let mut counter = self.round_robin_counter.fetch_add(1, Ordering::Relaxed);
        counter %= total_weight as usize;
        
        let mut current_weight = 0;
        for server in servers.iter().filter(|s| s.is_available()) {
            current_weight += server.weight as usize;
            if counter < current_weight {
                return Some(server.addr);
            }
        }
        
        None
    }
}
```

## Next Steps Preview

In Module 05, we'll add health checking:
- Periodic health checks for backend servers
- Automatic failover when servers become unhealthy
- Recovery detection and re-enabling of servers
- Configurable health check intervals and timeouts

## Key Takeaways

- **Round-Robin Algorithm**: Simple but effective for homogeneous environments
- **Thread Safety**: Atomic operations and mutexes enable safe concurrent access
- **Backend Pool**: Centralized management of backend servers and their state
- **Load Distribution**: Fair distribution of requests across healthy servers
- **Error Handling**: Graceful degradation when backends are unavailable

Our load balancer now distributes requests fairly across multiple backend servers, providing the foundation for high availability and scalability.

## Navigation
- [Previous: HTTP Protocol Implementation](03-http-protocol.md)
- [Next: Health Checking System](05-health-checking.md)
