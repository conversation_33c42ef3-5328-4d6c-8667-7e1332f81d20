# Module 13: Observability

## Learning Objectives
- Implement distributed tracing with OpenTelemetry
- Add detailed performance metrics collection
- Create health check endpoints
- Implement debugging and profiling tools

## Prerequisites
- Completion of Module 12: Request Transformation
- Understanding of observability concepts
- Familiarity with OpenTelemetry
- Knowledge of performance profiling

## Navigation
- [Previous: Request Transformation](12-request-transformation.md)
- [Next: Performance Optimization](14-performance-optimization.md)

---

## Distributed Tracing

Implement OpenTelemetry tracing:

```rust
use opentelemetry::{
    trace::{Span, Tracer, TracerProvider},
    Context, KeyValue,
};
use opentelemetry_otlp::WithExportConfig;
use tonic::metadata::MetadataMap;

pub struct TracingLayer {
    tracer: Tracer,
}

impl TracingLayer {
    pub fn new() -> Result<Self> {
        let tracer = opentelemetry_otlp::new_pipeline()
            .tracing()
            .with_exporter(
                opentelemetry_otlp::new_exporter()
                    .tonic()
                    .with_endpoint("http://otel-collector:4317")
            )
            .install_batch(opentelemetry::runtime::Tokio)?;

        Ok(Self { tracer })
    }

    pub async fn trace_request<B>(
        &self,
        request: &Request<B>,
    ) -> TracingContext {
        let mut span = self.tracer
            .span_builder("load_balancer_request")
            .with_attributes(vec![
                KeyValue::new("http.method", request.method().to_string()),
                KeyValue::new("http.url", request.uri().to_string()),
            ])
            .start(&self.tracer);

        // Extract parent context from headers
        if let Some(parent_context) = self.extract_context(request) {
            span.set_parent(parent_context);
        }

        TracingContext { span }
    }

    fn extract_context<B>(&self, request: &Request<B>) -> Option<Context> {
        let mut carrier = MetadataMap::new();
        for (key, value) in request.headers() {
            carrier.insert(key, value.as_bytes().into());
        }

        opentelemetry::global::get_text_map_propagator(|propagator| {
            propagator.extract(&carrier)
        })
    }
}

pub struct TracingContext {
    span: Span,
}

impl TracingContext {
    pub fn record_event(&self, name: &str, attributes: Vec<KeyValue>) {
        self.span.add_event(name, attributes);
    }

    pub fn set_status(&self, status: opentelemetry::trace::Status) {
        self.span.set_status(status);
    }
}
```

---

## Metrics Collection

Implement detailed metrics collection:

```rust
use metrics::{Counter, Gauge, Histogram, Key, KeyName, Recorder, Unit};
use metrics_util::layers::{Layer, Stack};
use std::time::Instant;

pub struct MetricsCollector {
    recorder: Stack<metrics_prometheus::Recorder>,
}

impl MetricsCollector {
    pub fn new() -> Self {
        let recorder = metrics_prometheus::Recorder::builder()
            .with_namespace("load_balancer")
            .build()
            .expect("failed to create prometheus recorder");

        Self {
            recorder: Stack::new(recorder),
        }
    }

    pub fn record_request_metrics(
        &self,
        duration: f64,
        status: u16,
        backend: &str,
    ) {
        // Request count
        self.recorder.increment_counter(
            Key::from_parts("requests_total", vec![
                ("backend", backend.to_string()),
                ("status", status.to_string()),
            ])
        );

        // Request duration
        self.recorder.record_histogram(
            Key::from_parts("request_duration_seconds", vec![
                ("backend", backend.to_string()),
            ]),
            duration,
        );

        // Active connections
        self.recorder.record_gauge(
            Key::from_parts("active_connections", vec![
                ("backend", backend.to_string()),
            ]),
            1.0,
        );
    }

    pub fn record_backend_metrics(&self, backend: &str, healthy: bool) {
        self.recorder.record_gauge(
            Key::from_parts("backend_healthy", vec![
                ("backend", backend.to_string()),
            ]),
            if healthy { 1.0 } else { 0.0 },
        );
    }
}
```

---

## Health Check API

Implement comprehensive health checks:

```rust
use warp::Filter;
use serde_json::json;

pub struct HealthApi {
    backends: Arc<Vec<Backend>>,
    metrics: Arc<MetricsCollector>,
}

impl HealthApi {
    pub fn routes(&self) -> impl Filter<Extract = impl warp::Reply> + Clone {
        let health_check = warp::path("health")
            .and(warp::get())
            .and_then(move || self.health_check());

        let metrics = warp::path("metrics")
            .and(warp::get())
            .and_then(move || self.metrics_endpoint());

        let ready = warp::path("ready")
            .and(warp::get())
            .and_then(move || self.readiness_check());

        health_check.or(metrics).or(ready)
    }

    async fn health_check(&self) -> Result<impl warp::Reply, warp::Rejection> {
        let mut status = json!({
            "status": "healthy",
            "timestamp": chrono::Utc::now().to_rfc3339(),
            "version": env!("CARGO_PKG_VERSION"),
            "backends": {}
        });

        let backends = status.as_object_mut().unwrap()
            .get_mut("backends")
            .unwrap()
            .as_object_mut()
            .unwrap();

        for backend in self.backends.iter() {
            backends.insert(
                backend.addr.to_string(),
                json!({
                    "healthy": backend.is_healthy(),
                    "last_check": backend.last_check(),
                    "error_count": backend.error_count(),
                })
            );
        }

        Ok(warp::reply::json(&status))
    }

    async fn metrics_endpoint(&self) -> Result<impl warp::Reply, warp::Rejection> {
        let metrics = self.metrics.gather();
        Ok(warp::reply::with_header(
            metrics,
            "content-type",
            "text/plain; version=0.0.4",
        ))
    }

    async fn readiness_check(&self) -> Result<impl warp::Reply, warp::Rejection> {
        let ready = self.backends.iter().any(|b| b.is_healthy());
        
        if ready {
            Ok(warp::reply::with_status(
                "ready",
                warp::http::StatusCode::OK,
            ))
        } else {
            Ok(warp::reply::with_status(
                "not ready",
                warp::http::StatusCode::SERVICE_UNAVAILABLE,
            ))
        }
    }
}
```

---

## Performance Profiling

Add profiling capabilities:

```rust
use pprof::ProfilerGuard;
use std::fs::File;
use std::time::Duration;

pub struct Profiler {
    guard: Option<ProfilerGuard<'static>>,
}

impl Profiler {
    pub fn new() -> Self {
        Self { guard: None }
    }

    pub fn start(&mut self) {
        self.guard = Some(ProfilerGuard::new(100).unwrap());
    }

    pub fn save_profile(&self, path: &str) -> Result<()> {
        if let Some(guard) = &self.guard {
            let report = guard.report().build()?;
            
            let file = File::create(path)?;
            report.write_protobuf(&file)?;
        }
        
        Ok(())
    }

    pub async fn periodic_profiling(&self, interval: Duration) {
        let mut interval = tokio::time::interval(interval);
        
        loop {
            interval.tick().await;
            
            let timestamp = chrono::Utc::now()
                .format("%Y%m%d_%H%M%S")
                .to_string();
                
            let path = format!("profiles/profile_{}.pb", timestamp);
            
            if let Err(e) = self.save_profile(&path) {
                error!("Failed to save profile: {}", e);
            }
        }
    }
}
```

---

## Debug Interface

Implement a debugging interface:

```rust
pub struct DebugInterface {
    state: Arc<AppState>,
}

impl DebugInterface {
    pub fn routes(&self) -> impl Filter<Extract = impl warp::Reply> + Clone {
        let connections = warp::path("debug")
            .and(warp::path("connections"))
            .and(warp::get())
            .and_then(move || self.list_connections());

        let backend_state = warp::path("debug")
            .and(warp::path("backends"))
            .and(warp::get())
            .and_then(move || self.backend_state());

        let request_log = warp::path("debug")
            .and(warp::path("requests"))
            .and(warp::get())
            .and_then(move || self.request_log());

        connections.or(backend_state).or(request_log)
    }

    async fn list_connections(&self) -> Result<impl warp::Reply, warp::Rejection> {
        let conns = self.state.active_connections.read().await;
        Ok(warp::reply::json(&*conns))
    }

    async fn backend_state(&self) -> Result<impl warp::Reply, warp::Rejection> {
        let state = self.state.backend_states.read().await;
        Ok(warp::reply::json(&*state))
    }

    async fn request_log(&self) -> Result<impl warp::Reply, warp::Rejection> {
        let log = self.state.request_log.read().await;
        Ok(warp::reply::json(&*log))
    }
}
```

---

## Integration

Combine all observability features:

```rust
pub struct ObservabilityLayer {
    tracing: TracingLayer,
    metrics: MetricsCollector,
    health_api: HealthApi,
    profiler: Profiler,
    debug: DebugInterface,
}

impl ObservabilityLayer {
    pub async fn instrument_request<B>(
        &self,
        request: Request<B>,
    ) -> Result<Response<B>, Error> {
        let start = Instant::now();
        let tracing_context = self.tracing.trace_request(&request).await;

        let response = self.handle_request(request).await;

        let duration = start.elapsed().as_secs_f64();
        let status = response.status().as_u16();

        self.metrics.record_request_metrics(
            duration,
            status,
            response.extensions().get::<Backend>().unwrap(),
        );

        tracing_context.set_status(status.into());

        Ok(response)
    }

    pub async fn start_observability(&self) {
        let health_server = self.health_api.routes();
        let debug_server = self.debug.routes();

        tokio::spawn(
            warp::serve(health_server.or(debug_server))
                .run(([0, 0, 0, 0], 9090))
        );

        self.profiler.periodic_profiling(Duration::from_secs(300));
    }
}
```

---

## Summary
- Implemented distributed tracing
- Added comprehensive metrics
- Created health check API
- Added profiling capabilities
- Implemented debugging interface

---

## Next Steps
Continue to [Module 14: Performance Optimization](14-performance-optimization.md) to learn about improving load balancer performance.
