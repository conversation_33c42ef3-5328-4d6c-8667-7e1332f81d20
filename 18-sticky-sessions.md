# 12 - Dynamic Backend Discovery

## Goal
Integrate with service discovery systems to update backend lists automatically.

## Concepts Introduced
- Service discovery basics
- Integrating with Consul, etcd, or DNS
- Handling dynamic changes

## Why Dynamic Discovery?
It enables the load balancer to adapt to infrastructure changes automatically, supporting cloud-native and microservices environments.

## Alternatives Considered
- Static backend lists (less flexible)
- Manual updates (error-prone)

## Next Steps
We'll implement weighted load balancing for more control over traffic distribution.
