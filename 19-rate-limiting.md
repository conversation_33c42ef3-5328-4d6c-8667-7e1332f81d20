# Module 19: Rate Limiting

## Learning Objectives
- Understand rate limiting concepts
- Implement token bucket algorithm
- Add sliding window rate limiting
- Create distributed rate limiting
- Handle rate limit responses

## Prerequisites
- Completion of Module 18: Caching and Storage
- Understanding of rate limiting algorithms
- Knowledge of distributed systems
- Familiarity with Redis

## Navigation
- [Previous: Caching and Storage](18-caching-storage.md)
- [Next: Circuit Breaking](20-circuit-breaking.md)

---

## Token Bucket Algorithm

Implement a basic token bucket rate limiter:

```rust
use std::time::{Duration, Instant};
use tokio::sync::Mutex;
use std::collections::HashMap;
use std::sync::Arc;

pub struct TokenBucket {
    capacity: u32,
    refill_rate: f64,
    tokens: f64,
    last_update: Instant,
}

impl TokenBucket {
    pub fn new(capacity: u32, refill_rate: f64) -> Self {
        Self {
            capacity,
            refill_rate,
            tokens: capacity as f64,
            last_update: Instant::now(),
        }
    }

    pub fn try_acquire(&mut self, tokens: u32) -> bool {
        self.refill();
        
        if self.tokens >= tokens as f64 {
            self.tokens -= tokens as f64;
            true
        } else {
            false
        }
    }

    fn refill(&mut self) {
        let now = Instant::now();
        let elapsed = now.duration_since(self.last_update);
        let new_tokens = elapsed.as_secs_f64() * self.refill_rate;
        
        self.tokens = (self.tokens + new_tokens)
            .min(self.capacity as f64);
        self.last_update = now;
    }
}

pub struct RateLimiter {
    buckets: Arc<Mutex<HashMap<String, TokenBucket>>>,
    capacity: u32,
    refill_rate: f64,
}

impl RateLimiter {
    pub fn new(capacity: u32, refill_rate: f64) -> Self {
        Self {
            buckets: Arc::new(Mutex::new(HashMap::new())),
            capacity,
            refill_rate,
        }
    }

    pub async fn is_allowed(
        &self,
        key: &str,
        tokens: u32,
    ) -> bool {
        let mut buckets = self.buckets.lock().await;
        
        let bucket = buckets
            .entry(key.to_string())
            .or_insert_with(|| {
                TokenBucket::new(self.capacity, self.refill_rate)
            });
            
        bucket.try_acquire(tokens)
    }

    pub async fn cleanup(&self, max_idle: Duration) {
        let mut buckets = self.buckets.lock().await;
        buckets.retain(|_, bucket| {
            bucket.last_update.elapsed() <= max_idle
        });
    }
}
```

---

## Sliding Window Rate Limiting

Implement a sliding window rate limiter using Redis:

```rust
use redis::{Commands, Connection, RedisResult};
use std::time::{SystemTime, UNIX_EPOCH};

pub struct SlidingWindowRateLimiter {
    redis: Connection,
    window: Duration,
    max_requests: u32,
}

impl SlidingWindowRateLimiter {
    pub fn new(
        redis_url: &str,
        window: Duration,
        max_requests: u32,
    ) -> RedisResult<Self> {
        let client = redis::Client::open(redis_url)?;
        let redis = client.get_connection()?;
        
        Ok(Self {
            redis,
            window,
            max_requests,
        })
    }

    pub fn is_allowed(&mut self, key: &str) -> RedisResult<bool> {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
            
        let window_start = now - self.window.as_secs();
        
        // Remove old entries
        self.redis.zremrangebyscore(
            key,
            0,
            window_start as f64,
        )?;
        
        // Count requests in window
        let count: u32 = self.redis.zcard(key)?;
        
        if count < self.max_requests {
            // Add new request
            self.redis.zadd(key, now as f64, now.to_string())?;
            
            // Set expiry
            self.redis.expire(
                key,
                self.window.as_secs() as usize,
            )?;
            
            Ok(true)
        } else {
            Ok(false)
        }
    }

    pub fn get_remaining(
        &mut self,
        key: &str,
    ) -> RedisResult<u32> {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
            
        let window_start = now - self.window.as_secs();
        
        // Remove old entries
        self.redis.zremrangebyscore(
            key,
            0,
            window_start as f64,
        )?;
        
        // Count remaining requests
        let count: u32 = self.redis.zcard(key)?;
        Ok(self.max_requests.saturating_sub(count))
    }
}
```

---

## Distributed Rate Limiting

Implement distributed rate limiting with Redis:

```rust
use redis::{pipe, Commands, Connection, RedisResult};
use std::time::{Duration, SystemTime, UNIX_EPOCH};

pub struct DistributedRateLimiter {
    redis: Connection,
    window: Duration,
    max_requests: u32,
    drift_factor: f64,
}

impl DistributedRateLimiter {
    pub fn new(
        redis_url: &str,
        window: Duration,
        max_requests: u32,
        drift_factor: f64,
    ) -> RedisResult<Self> {
        let client = redis::Client::open(redis_url)?;
        let redis = client.get_connection()?;
        
        Ok(Self {
            redis,
            window,
            max_requests,
            drift_factor,
        })
    }

    pub fn is_allowed(
        &mut self,
        key: &str,
    ) -> RedisResult<RateLimitResponse> {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs();
            
        let window_start = now - self.window.as_secs();
        
        // Execute atomic operations
        let results: (u32, Vec<String>) = pipe()
            .atomic()
            // Remove old entries
            .zremrangebyscore(key, 0, window_start as f64)
            // Get current count
            .zcard(key)
            // Get all timestamps
            .zrange(key, 0, -1)
            .query(&mut self.redis)?;
            
        let count = results.0;
        let timestamps = results.1;
        
        if count < self.max_requests {
            // Add new request
            self.redis.zadd(key, now as f64, now.to_string())?;
            
            // Set expiry
            self.redis.expire(
                key,
                self.window.as_secs() as usize,
            )?;
            
            Ok(RateLimitResponse {
                is_allowed: true,
                remaining: self.max_requests - count - 1,
                reset_after: self.window,
                retry_after: None,
            })
        } else {
            // Calculate retry after
            let oldest: u64 = timestamps[0].parse().unwrap();
            let retry_after = Duration::from_secs(
                oldest + self.window.as_secs() - now,
            );
            
            Ok(RateLimitResponse {
                is_allowed: false,
                remaining: 0,
                reset_after: retry_after,
                retry_after: Some(retry_after),
            })
        }
    }
}

pub struct RateLimitResponse {
    pub is_allowed: bool,
    pub remaining: u32,
    pub reset_after: Duration,
    pub retry_after: Option<Duration>,
}
```

---

## Rate Limit Headers

Add rate limit headers to responses:

```rust
use hyper::{Body, Response, StatusCode};
use http::header::{HeaderName, HeaderValue};

pub trait RateLimitHeaders {
    fn add_rate_limit_headers(
        self,
        response: RateLimitResponse,
    ) -> Self;
}

impl RateLimitHeaders for Response<Body> {
    fn add_rate_limit_headers(
        mut self,
        response: RateLimitResponse,
    ) -> Self {
        let headers = self.headers_mut();
        
        // Add standard headers
        headers.insert(
            "X-RateLimit-Limit",
            HeaderValue::from_str(&response.limit.to_string())
                .unwrap(),
        );
        
        headers.insert(
            "X-RateLimit-Remaining",
            HeaderValue::from_str(&response.remaining.to_string())
                .unwrap(),
        );
        
        headers.insert(
            "X-RateLimit-Reset",
            HeaderValue::from_str(
                &response.reset_after.as_secs().to_string(),
            )
            .unwrap(),
        );
        
        // Add retry-after if rate limited
        if let Some(retry_after) = response.retry_after {
            headers.insert(
                "Retry-After",
                HeaderValue::from_str(
                    &retry_after.as_secs().to_string(),
                )
                .unwrap(),
            );
            
            *self.status_mut() = StatusCode::TOO_MANY_REQUESTS;
        }
        
        self
    }
}
```

---

## Rate Limit Middleware

Create a rate limit middleware:

```rust
use std::future::Future;
use std::pin::Pin;
use std::sync::Arc;
use std::task::{Context, Poll};
use hyper::{Body, Request, Response};
use tower::{Layer, Service};

pub struct RateLimitLayer {
    limiter: Arc<DistributedRateLimiter>,
}

impl RateLimitLayer {
    pub fn new(limiter: DistributedRateLimiter) -> Self {
        Self {
            limiter: Arc::new(limiter),
        }
    }
}

impl<S> Layer<S> for RateLimitLayer {
    type Service = RateLimitService<S>;

    fn layer(&self, service: S) -> Self::Service {
        RateLimitService {
            inner: service,
            limiter: self.limiter.clone(),
        }
    }
}

pub struct RateLimitService<S> {
    inner: S,
    limiter: Arc<DistributedRateLimiter>,
}

impl<S> Service<Request<Body>> for RateLimitService<S>
where
    S: Service<Request<Body>, Response = Response<Body>>,
{
    type Response = S::Response;
    type Error = S::Error;
    type Future = Pin<Box<dyn Future<
        Output = Result<Self::Response, Self::Error>,
    > + Send>>;

    fn poll_ready(
        &mut self,
        cx: &mut Context<'_>,
    ) -> Poll<Result<(), Self::Error>> {
        self.inner.poll_ready(cx)
    }

    fn call(&mut self, request: Request<Body>) -> Self::Future {
        let limiter = self.limiter.clone();
        let mut inner = self.inner.clone();
        
        Box::pin(async move {
            // Get client identifier
            let client_id = get_client_id(&request);
            
            // Check rate limit
            let response = limiter
                .is_allowed(&client_id)
                .await?;
                
            if !response.is_allowed {
                // Return rate limit response
                return Ok(Response::builder()
                    .status(StatusCode::TOO_MANY_REQUESTS)
                    .add_rate_limit_headers(response)
                    .body(Body::empty())?);
            }
            
            // Forward request
            let response = inner.call(request).await?;
            
            // Add rate limit headers
            Ok(response.add_rate_limit_headers(response))
        })
    }
}

fn get_client_id(request: &Request<Body>) -> String {
    // Get client IP from X-Forwarded-For or remote addr
    request
        .headers()
        .get("X-Forwarded-For")
        .and_then(|h| h.to_str().ok())
        .map(|s| s.split(',').next().unwrap_or(""))
        .unwrap_or("")
        .to_string()
}
```

---

## Summary
- Implemented token bucket algorithm
- Added sliding window rate limiting
- Created distributed rate limiting
- Added rate limit headers
- Implemented rate limit middleware

---

## Next Steps
Continue to [Module 20: Circuit Breaking](20-circuit-breaking.md) to learn about implementing circuit breakers.
