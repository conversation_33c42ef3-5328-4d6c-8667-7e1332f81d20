# Module 05: Health Checking System

## Learning Objectives
- Understand health checking strategies and their importance in load balancing
- Implement periodic health checks using Tokio timers and intervals
- Learn about circuit breaker patterns for fault tolerance
- Build configurable health check mechanisms (HTTP, TCP, custom)
- Handle backend server failures and recovery automatically
- Implement exponential backoff and jitter for resilient systems

## Prerequisites
- Completion of Module 04: Round-Robin Load Balancing
- Understanding of async programming and Tokio runtime
- Familiarity with HTTP client programming and error handling
- Knowledge of concurrent programming patterns in Rust

## Navigation
- [Previous: Round-Robin Load Balancing](04-round-robin-balancing.md)
- [Next: Configuration System](06-configuration-system.md)

## Health Checking Fundamentals

Health checking is critical for maintaining high availability in load-balanced systems. It ensures that traffic is only sent to servers capable of handling requests.

### Why Health Checking Matters

```mermaid
flowchart TD
    subgraph "Without Health Checking"
        LB1[Load Balancer]
        LB1 --> S1[Server 1 ✓]
        LB1 --> S2[Server 2 ✗]
        LB1 --> S3[Server 3 ✓]
        S2 --> E1[Failed Requests]
    end
    
    subgraph "With Health Checking"
        LB2[Load Balancer + Health Checks]
        LB2 --> S4[Server 1 ✓]
        LB2 -.-> S5[Server 2 ✗ Excluded]
        LB2 --> S6[Server 3 ✓]
        S4 --> Success1[Successful Requests]
        S6 --> Success2[Successful Requests]
    end
```

**Benefits**:
- **Automatic Failover**: Remove failed servers from rotation
- **Improved Reliability**: Reduce failed requests to clients
- **Faster Recovery**: Detect when servers come back online
- **Better User Experience**: Consistent response times

### Health Check Types

**Active Health Checks**:
- Proactively test server health
- Send periodic requests to health endpoints
- More accurate but generates additional load

**Passive Health Checks**:
- Monitor actual request success/failure rates
- No additional load on servers
- May be slower to detect failures

**Hybrid Approach**:
- Combine both active and passive checks
- Use passive for quick detection, active for confirmation

## Health Check Strategies

### HTTP Health Checks

Most common for web applications:

```http
GET /health HTTP/1.1
Host: backend-server.com

HTTP/1.1 200 OK
Content-Type: application/json

{
  "status": "healthy",
  "timestamp": "2023-10-01T12:00:00Z",
  "checks": {
    "database": "ok",
    "cache": "ok"
  }
}
```

### TCP Health Checks

Simple connection-based checks:
- Attempt to establish TCP connection
- Faster than HTTP checks
- Less informative about application health

### Custom Health Checks

Application-specific logic:
- Database connectivity
- External service dependencies
- Resource utilization thresholds

## Implementation: Health Checking System

Let's implement a comprehensive health checking system.

### Health Check Configuration

Create `src/health/mod.rs`:

```rust
//! Health checking system for backend servers
//! 
//! This module provides active and passive health checking capabilities
//! to ensure traffic is only routed to healthy backend servers.

pub mod checker;
pub mod config;

pub use checker::HealthChecker;
pub use config::{HealthCheckConfig, HealthCheckType};

use std::time::Duration;
use serde::{Deserialize, Serialize};

/// Health check configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheckConfig {
    /// Type of health check to perform
    pub check_type: HealthCheckType,
    /// Interval between health checks
    pub interval: Duration,
    /// Timeout for each health check
    pub timeout: Duration,
    /// Number of consecutive failures before marking unhealthy
    pub failure_threshold: u32,
    /// Number of consecutive successes before marking healthy
    pub success_threshold: u32,
    /// Path for HTTP health checks
    pub path: String,
    /// Expected HTTP status codes for success
    pub expected_status: Vec<u16>,
}

/// Types of health checks supported
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum HealthCheckType {
    /// HTTP GET request to health endpoint
    Http,
    /// TCP connection attempt
    Tcp,
    /// Custom health check logic
    Custom,
}

impl Default for HealthCheckConfig {
    fn default() -> Self {
        Self {
            check_type: HealthCheckType::Http,
            interval: Duration::from_secs(30),
            timeout: Duration::from_secs(5),
            failure_threshold: 3,
            success_threshold: 2,
            path: "/health".to_string(),
            expected_status: vec![200],
        }
    }
}

/// Result of a health check
#[derive(Debug, Clone)]
pub struct HealthCheckResult {
    pub success: bool,
    pub response_time: Duration,
    pub error: Option<String>,
    pub timestamp: std::time::Instant,
}
```

### Health Checker Implementation

Create `src/health/checker.rs`:

```rust
use super::{HealthCheckConfig, HealthCheckType, HealthCheckResult};
use crate::backend::{Backend, BackendStatus, BackendPool};
use hyper::{Client, Uri};
use hyper::client::HttpConnector;
use std::collections::HashMap;
use std::net::SocketAddr;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::net::TcpStream;
use tokio::time::{interval, timeout};
use tracing::{info, warn, error, debug};

/// Health checker that monitors backend server health
pub struct HealthChecker {
    config: HealthCheckConfig,
    backend_pool: Arc<BackendPool>,
    client: Client<HttpConnector>,
    /// Track consecutive failures/successes per backend
    backend_states: Arc<tokio::sync::Mutex<HashMap<SocketAddr, BackendHealthState>>>,
}

/// Internal state tracking for each backend
#[derive(Debug, Clone)]
struct BackendHealthState {
    consecutive_failures: u32,
    consecutive_successes: u32,
    last_check: Option<Instant>,
    current_status: BackendStatus,
}

impl BackendHealthState {
    fn new() -> Self {
        Self {
            consecutive_failures: 0,
            consecutive_successes: 0,
            last_check: None,
            current_status: BackendStatus::Healthy,
        }
    }
}

impl HealthChecker {
    /// Create a new health checker
    pub fn new(config: HealthCheckConfig, backend_pool: Arc<BackendPool>) -> Self {
        let client = Client::builder()
            .pool_idle_timeout(config.timeout)
            .build::<_, hyper::Body>(HttpConnector::new());
        
        Self {
            config,
            backend_pool,
            client,
            backend_states: Arc::new(tokio::sync::Mutex::new(HashMap::new())),
        }
    }
    
    /// Start the health checking process
    pub async fn start(&self) {
        info!("Starting health checker with interval: {:?}", self.config.interval);
        
        let mut interval = interval(self.config.interval);
        
        loop {
            interval.tick().await;
            self.check_all_backends().await;
        }
    }
    
    /// Check health of all backends
    async fn check_all_backends(&self) {
        let backends = self.backend_pool.get_all_backends();
        
        if backends.is_empty() {
            debug!("No backends to check");
            return;
        }
        
        info!("Checking health of {} backends", backends.len());
        
        // Check all backends concurrently
        let mut tasks = Vec::new();
        
        for backend in backends {
            let checker = self.clone_for_task();
            let task = tokio::spawn(async move {
                checker.check_backend_health(backend.addr).await
            });
            tasks.push((backend.addr, task));
        }
        
        // Wait for all health checks to complete
        for (addr, task) in tasks {
            match task.await {
                Ok(result) => {
                    self.process_health_check_result(addr, result).await;
                }
                Err(e) => {
                    error!("Health check task failed for {}: {}", addr, e);
                }
            }
        }
    }
    
    /// Check health of a single backend
    async fn check_backend_health(&self, addr: SocketAddr) -> HealthCheckResult {
        let start_time = Instant::now();
        
        let result = match self.config.check_type {
            HealthCheckType::Http => self.http_health_check(addr).await,
            HealthCheckType::Tcp => self.tcp_health_check(addr).await,
            HealthCheckType::Custom => self.custom_health_check(addr).await,
        };
        
        let response_time = start_time.elapsed();
        
        match result {
            Ok(_) => {
                debug!("Health check succeeded for {} in {:?}", addr, response_time);
                HealthCheckResult {
                    success: true,
                    response_time,
                    error: None,
                    timestamp: start_time,
                }
            }
            Err(e) => {
                debug!("Health check failed for {} in {:?}: {}", addr, response_time, e);
                HealthCheckResult {
                    success: false,
                    response_time,
                    error: Some(e.to_string()),
                    timestamp: start_time,
                }
            }
        }
    }
    
    /// Perform HTTP health check
    async fn http_health_check(&self, addr: SocketAddr) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        let uri: Uri = format!("http://{}{}", addr, self.config.path).parse()?;
        
        let response = timeout(self.config.timeout, self.client.get(uri)).await??;
        
        let status_code = response.status().as_u16();
        
        if self.config.expected_status.contains(&status_code) {
            Ok(())
        } else {
            Err(format!("Unexpected status code: {}", status_code).into())
        }
    }
    
    /// Perform TCP health check
    async fn tcp_health_check(&self, addr: SocketAddr) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        timeout(self.config.timeout, TcpStream::connect(addr)).await??;
        Ok(())
    }
    
    /// Perform custom health check (placeholder)
    async fn custom_health_check(&self, _addr: SocketAddr) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
        // Implement custom logic here
        Ok(())
    }
    
    /// Process health check result and update backend status
    async fn process_health_check_result(&self, addr: SocketAddr, result: HealthCheckResult) {
        let mut states = self.backend_states.lock().await;
        let state = states.entry(addr).or_insert_with(BackendHealthState::new);
        
        state.last_check = Some(result.timestamp);
        
        if result.success {
            state.consecutive_failures = 0;
            state.consecutive_successes += 1;
            
            // Check if we should mark as healthy
            if state.current_status != BackendStatus::Healthy 
                && state.consecutive_successes >= self.config.success_threshold {
                
                info!("Backend {} recovered after {} successful checks", 
                      addr, state.consecutive_successes);
                
                state.current_status = BackendStatus::Healthy;
                self.backend_pool.update_backend_status(addr, BackendStatus::Healthy);
            }
        } else {
            state.consecutive_successes = 0;
            state.consecutive_failures += 1;
            
            // Check if we should mark as unhealthy
            if state.current_status == BackendStatus::Healthy 
                && state.consecutive_failures >= self.config.failure_threshold {
                
                warn!("Backend {} failed after {} consecutive failures: {}", 
                      addr, state.consecutive_failures, 
                      result.error.unwrap_or_else(|| "Unknown error".to_string()));
                
                state.current_status = BackendStatus::Unhealthy;
                self.backend_pool.update_backend_status(addr, BackendStatus::Unhealthy);
            }
        }
    }
    
    /// Clone health checker for async task (lightweight clone)
    fn clone_for_task(&self) -> Self {
        Self {
            config: self.config.clone(),
            backend_pool: Arc::clone(&self.backend_pool),
            client: self.client.clone(),
            backend_states: Arc::clone(&self.backend_states),
        }
    }
    
    /// Get health statistics
    pub async fn get_health_stats(&self) -> HealthStats {
        let states = self.backend_states.lock().await;
        let backends = self.backend_pool.get_all_backends();
        
        let mut stats = HealthStats::default();
        stats.total_backends = backends.len();
        
        for backend in backends {
            match backend.status {
                BackendStatus::Healthy => stats.healthy_backends += 1,
                BackendStatus::Unhealthy => stats.unhealthy_backends += 1,
                BackendStatus::Draining => stats.draining_backends += 1,
                BackendStatus::Disabled => stats.disabled_backends += 1,
            }
        }
        
        stats.total_checks = states.len();
        stats
    }
}

/// Health check statistics
#[derive(Debug, Default)]
pub struct HealthStats {
    pub total_backends: usize,
    pub healthy_backends: usize,
    pub unhealthy_backends: usize,
    pub draining_backends: usize,
    pub disabled_backends: usize,
    pub total_checks: usize,
}
```

## Circuit Breaker Pattern

For additional resilience, implement a circuit breaker:

```rust
/// Circuit breaker states
#[derive(Debug, Clone, PartialEq)]
pub enum CircuitState {
    Closed,    // Normal operation
    Open,      // Failing, reject requests
    HalfOpen,  // Testing if service recovered
}

pub struct CircuitBreaker {
    state: CircuitState,
    failure_count: u32,
    failure_threshold: u32,
    timeout: Duration,
    last_failure_time: Option<Instant>,
}

impl CircuitBreaker {
    pub fn should_allow_request(&mut self) -> bool {
        match self.state {
            CircuitState::Closed => true,
            CircuitState::Open => {
                if let Some(last_failure) = self.last_failure_time {
                    if last_failure.elapsed() > self.timeout {
                        self.state = CircuitState::HalfOpen;
                        true
                    } else {
                        false
                    }
                } else {
                    false
                }
            }
            CircuitState::HalfOpen => true,
        }
    }
    
    pub fn record_success(&mut self) {
        self.failure_count = 0;
        self.state = CircuitState::Closed;
    }
    
    pub fn record_failure(&mut self) {
        self.failure_count += 1;
        self.last_failure_time = Some(Instant::now());
        
        if self.failure_count >= self.failure_threshold {
            self.state = CircuitState::Open;
        }
    }
}
```

## Integration with Load Balancer

Update the main application to include health checking:

```rust
// Update src/main.rs
use rusty_balancer::{
    proxy::HttpProxy,
    backend::BackendPool,
    health::{HealthChecker, HealthCheckConfig},
};
use std::net::SocketAddr;
use std::sync::Arc;
use tracing::info;

#[tokio::main]
async fn main() -> rusty_balancer::Result<()> {
    tracing_subscriber::fmt::init();
    
    info!("Starting Rusty Load Balancer with Health Checking");
    
    // Create backend pool
    let backend_pool = Arc::new(BackendPool::new());
    
    // Add backend servers
    let backends = vec![
        "127.0.0.1:8081".parse::<SocketAddr>()?,
        "127.0.0.1:8082".parse::<SocketAddr>()?,
        "127.0.0.1:8083".parse::<SocketAddr>()?,
    ];
    
    for backend in backends {
        backend_pool.add_backend(backend)?;
    }
    
    // Create health checker
    let health_config = HealthCheckConfig::default();
    let health_checker = HealthChecker::new(health_config, Arc::clone(&backend_pool));
    
    // Start health checking in background
    let health_checker_clone = health_checker.clone_for_task();
    tokio::spawn(async move {
        health_checker_clone.start().await;
    });
    
    // Create and start proxy
    let bind_addr: SocketAddr = "127.0.0.1:8080".parse()?;
    let proxy = HttpProxy::new(backend_pool);
    
    proxy.start(bind_addr).await?;
    
    Ok(())
}
```

## Testing Health Checking

### Failure Simulation

1. **Start all backends and load balancer**
2. **Stop one backend server** (Ctrl+C)
3. **Observe health check logs** showing failure detection
4. **Verify traffic stops going to failed server**
5. **Restart the backend server**
6. **Observe recovery detection** and traffic resumption

### Load Testing with Failures

```bash
# Generate continuous load
while true; do
    curl -s http://127.0.0.1:8080/ > /dev/null
    sleep 0.1
done

# In another terminal, simulate failures
# Stop/start backend servers and observe behavior
```

## Advanced Health Check Features

### Exponential Backoff

```rust
impl HealthChecker {
    fn calculate_next_check_interval(&self, consecutive_failures: u32) -> Duration {
        let base_interval = self.config.interval;
        let backoff_multiplier = 2_u64.pow(consecutive_failures.min(5));
        let max_interval = Duration::from_secs(300); // 5 minutes max
        
        std::cmp::min(base_interval * backoff_multiplier, max_interval)
    }
}
```

### Jitter for Thundering Herd Prevention

```rust
use rand::Rng;

fn add_jitter(duration: Duration) -> Duration {
    let mut rng = rand::thread_rng();
    let jitter_ms = rng.gen_range(0..1000); // 0-1 second jitter
    duration + Duration::from_millis(jitter_ms)
}
```

## Next Steps Preview

In Module 06, we'll implement a configuration system:
- YAML/TOML configuration files
- Environment variable support
- Hot-reload capabilities
- Validation and error handling

## Key Takeaways

- **Proactive Monitoring**: Health checks prevent routing to failed servers
- **Configurable Thresholds**: Tune sensitivity to avoid false positives
- **Automatic Recovery**: Detect when servers come back online
- **Circuit Breaker**: Additional protection against cascading failures
- **Concurrent Checks**: Async programming enables efficient health monitoring

Health checking transforms our load balancer from a simple request distributor into a resilient, self-healing system that maintains high availability even when individual servers fail.

## Navigation
- [Previous: Round-Robin Load Balancing](04-round-robin-balancing.md)
- [Next: Configuration System](06-configuration-system.md)
