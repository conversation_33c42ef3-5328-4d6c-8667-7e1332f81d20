# Module 08: TLS and Security

## Learning Objectives
- Implement TLS termination for HTTPS traffic
- Configure SSL/TLS certificates and key management
- Add security headers and request validation
- Implement rate limiting and DDoS protection

## Prerequisites
- Completion of Module 07: Logging and Metrics
- Understanding of TLS/SSL concepts
- Familiarity with security best practices
- Knowledge of cryptography basics

## Navigation
- [Previous: Logging and Metrics](07-logging-metrics.md)
- [Next: Advanced Load Balancing](09-advanced-balancing.md)

---

## TLS Implementation

First, let's add TLS support using the `rustls` crate:

```rust
use rustls::{ServerConfig, Certificate, PrivateKey};
use rustls_pemfile::{certs, pkcs8_private_keys};
use std::fs::File;
use std::io::BufReader;
use hyper::server::conn::Http;
use tokio_rustls::TlsAcceptor;

pub struct TlsConfig {
    cert_path: PathBuf,
    key_path: Path<PERSON>uf,
}

impl TlsConfig {
    pub fn new(cert_path: PathBuf, key_path: PathBuf) -> Self {
        Self {
            cert_path,
            key_path,
        }
    }

    /// Load TLS configuration
    pub fn load_config(&self) -> Result<ServerConfig> {
        // Load certificate
        let cert_file = File::open(&self.cert_path)?;
        let mut cert_reader = BufReader::new(cert_file);
        let certs = certs(&mut cert_reader)?
            .into_iter()
            .map(Certificate)
            .collect();

        // Load private key
        let key_file = File::open(&self.key_path)?;
        let mut key_reader = BufReader::new(key_file);
        let key = PrivateKey(pkcs8_private_keys(&mut key_reader)?.remove(0));

        // Create TLS config
        let config = ServerConfig::builder()
            .with_safe_defaults()
            .with_no_client_auth()
            .with_single_cert(certs, key)?;

        Ok(config)
    }
}
```

---

## Security Headers

Implement security headers middleware:

```rust
pub struct SecurityHeaders;

impl SecurityHeaders {
    pub fn new() -> Self {
        Self
    }

    pub fn apply_headers(&self, response: &mut Response<Body>) {
        let headers = response.headers_mut();
        
        // Add security headers
        headers.insert(
            "Strict-Transport-Security",
            "max-age=31536000; includeSubDomains".parse().unwrap(),
        );
        
        headers.insert(
            "X-Content-Type-Options",
            "nosniff".parse().unwrap(),
        );
        
        headers.insert(
            "X-Frame-Options",
            "DENY".parse().unwrap(),
        );
        
        headers.insert(
            "X-XSS-Protection",
            "1; mode=block".parse().unwrap(),
        );
        
        headers.insert(
            "Content-Security-Policy",
            "default-src 'self'".parse().unwrap(),
        );
    }
}
```

---

## Rate Limiting

Implement token bucket rate limiting:

```rust
use std::sync::Arc;
use tokio::sync::Mutex;
use std::collections::HashMap;
use std::time::{Instant, Duration};

pub struct RateLimiter {
    // Tokens per second
    rate: f64,
    // Maximum burst size
    capacity: u32,
    // Current tokens per client
    tokens: Arc<Mutex<HashMap<String, (f64, Instant)>>>,
}

impl RateLimiter {
    pub fn new(rate: f64, capacity: u32) -> Self {
        Self {
            rate,
            capacity,
            tokens: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    pub async fn check_rate_limit(&self, client_ip: &str) -> bool {
        let mut tokens = self.tokens.lock().await;
        let now = Instant::now();

        let (mut current_tokens, last_update) = tokens
            .get(client_ip)
            .copied()
            .unwrap_or((self.capacity as f64, now));

        // Add tokens based on time elapsed
        let elapsed = now.duration_since(last_update).as_secs_f64();
        current_tokens = (current_tokens + elapsed * self.rate)
            .min(self.capacity as f64);

        if current_tokens >= 1.0 {
            current_tokens -= 1.0;
            tokens.insert(client_ip.to_string(), (current_tokens, now));
            true
        } else {
            false
        }
    }
}
```

---

## DDoS Protection

Basic DDoS protection middleware:

```rust
pub struct DdosProtection {
    rate_limiter: Arc<RateLimiter>,
    blacklist: Arc<Mutex<HashMap<String, Instant>>>,
    threshold: u32,
    ban_duration: Duration,
}

impl DdosProtection {
    pub fn new(
        rate: f64,
        capacity: u32,
        threshold: u32,
        ban_duration: Duration,
    ) -> Self {
        Self {
            rate_limiter: Arc::new(RateLimiter::new(rate, capacity)),
            blacklist: Arc::new(Mutex::new(HashMap::new())),
            threshold,
            ban_duration,
        }
    }

    pub async fn check_request(&self, client_ip: &str) -> Result<()> {
        // Check blacklist
        let mut blacklist = self.blacklist.lock().await;
        if let Some(ban_time) = blacklist.get(client_ip) {
            if ban_time.elapsed() < self.ban_duration {
                return Err(Error::RateLimited);
            }
            blacklist.remove(client_ip);
        }

        // Check rate limit
        if !self.rate_limiter.check_rate_limit(client_ip).await {
            // Increment violation count
            let violations = self.increment_violations(client_ip).await;
            if violations >= self.threshold {
                blacklist.insert(client_ip.to_string(), Instant::now());
            }
            return Err(Error::RateLimited);
        }

        Ok(())
    }
}
```

---

## Integration with Load Balancer

Update the load balancer to use these security features:

```rust
pub struct SecureLoadBalancer {
    tls_config: TlsConfig,
    security_headers: SecurityHeaders,
    ddos_protection: DdosProtection,
}

impl SecureLoadBalancer {
    pub async fn run(self) -> Result<()> {
        let tls_config = self.tls_config.load_config()?;
        let acceptor = TlsAcceptor::from(Arc::new(tls_config));
        
        let server = Server::bind(&self.bind_addr).serve(make_service_fn(|conn| {
            let remote_addr = conn.remote_addr();
            let ddos_protection = self.ddos_protection.clone();
            let security_headers = self.security_headers.clone();
            
            async move {
                // Check DDoS protection
                ddos_protection.check_request(&remote_addr.ip().to_string()).await?;
                
                // Handle request with security headers
                Ok::<_, Error>(service_fn(move |mut req| {
                    security_headers.apply_headers(&mut req);
                    handle_request(req)
                }))
            }
        }));

        server.await?;
        Ok(())
    }
}
```

---

## Certificate Management

Examples of working with certificates:

1. **Self-signed Certificate**:
```bash
openssl req -x509 -newkey rsa:4096 -nodes \
    -keyout private.key -out cert.pem -days 365 \
    -subj "/CN=localhost"
```

2. **Let's Encrypt Integration**:
```rust
use acme_client::{Directory, DirectoryUrl};

async fn get_lets_encrypt_cert() -> Result<(Vec<u8>, Vec<u8>)> {
    let url = DirectoryUrl::LetsEncrypt;
    let dir = Directory::from_url(url).await?;
    // ... certificate acquisition logic
}
```

---

## Summary
- TLS termination with rustls
- Security headers implementation
- Rate limiting and DDoS protection
- Certificate management

---

## Next Steps
Continue to [Module 09: Advanced Load Balancing](09-advanced-balancing.md) to learn about sophisticated load balancing algorithms.
