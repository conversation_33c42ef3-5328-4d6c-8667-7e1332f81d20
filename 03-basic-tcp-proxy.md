# Module 02: Building a Basic TCP Proxy

## Learning Objectives
- Understand TCP proxy fundamentals and connection forwarding
- Implement async TCP connection handling with Tokio
- Learn Rust's ownership model in the context of network programming
- Build bidirectional data forwarding between client and server
- Handle connection lifecycle and error scenarios gracefully
- Implement proper resource cleanup and connection management

## Prerequisites
- Completion of Module 01: Project Setup and Architecture
- Basic understanding of TCP/IP networking concepts
- Familiarity with Rust's ownership, borrowing, and async/await syntax
- Understanding of Result types and error handling in Rust

## Navigation
- [Previous: Project Setup and Architecture](01-project-setup.md)
- [Next: HTTP Protocol Implementation](03-http-protocol.md)


## Understanding TCP Proxying: Rationale and Use Cases

A TCP proxy is the simplest form of a load balancer. It operates at the transport layer (Layer 4), forwarding raw TCP streams between clients and backend servers. This makes it protocol-agnostic: it can handle HTTP, database traffic, or any other TCP-based protocol.

**Why start with a TCP proxy?**
- **Foundation for All Proxies:** All higher-level proxies (HTTP, HTTPS, etc.) build on the ability to forward TCP connections.
- **Performance:** TCP proxies are fast and introduce minimal overhead.
- **Simplicity:** They don’t need to parse or understand application data, making them easy to implement and reason about.

**Alternatives:**
- You could start with an HTTP proxy, but that adds complexity (parsing, header management) before you’ve mastered the basics of async networking and connection handling.

**Real-World Use Cases:**
- Database proxying (e.g., for PostgreSQL, MySQL)
- Generic TCP load balancing (e.g., for legacy protocols)
- As a building block for more advanced, protocol-aware proxies


### TCP Proxy Architecture: How Data Flows

```mermaid
sequenceDiagram
    participant C as Client
    participant P as TCP Proxy
    participant S as Server
    
    C->>P: Connect
    P->>S: Connect to Backend
    S-->>P: Connection Established
    P-->>C: Connection Established
    
    loop Data Exchange
        C->>P: Send Data
        P->>S: Forward Data
        S->>P: Response Data
        P->>C: Forward Response
    end
    
    C->>P: Close Connection
    P->>S: Close Backend Connection
```


### Key Concepts and Strategies

- **Bidirectional Forwarding:** Data must flow from client to backend and from backend to client, simultaneously. This requires careful handling of async reads and writes.
- **Connection Multiplexing:** The proxy must handle many concurrent connections. We use async tasks (via Tokio) to achieve this efficiently.
- **Transparent Proxying:** The client should not be able to tell it’s talking to a proxy, not the real server.
- **Connection Pooling (Advanced):** For now, we’ll open a new backend connection per client. Later, we’ll add pooling for efficiency.

**Design Decision:**
We use Tokio’s async runtime and spawn a new task for each connection. This is scalable and leverages Rust’s safety guarantees.


## Rust Concepts in Focus: Why Async and Ownership Matter

### Async Programming with Tokio

Tokio is the de facto async runtime for Rust. It allows us to handle thousands of connections with a small number of threads, using async/await syntax for clarity and safety.

**Why not threads?**
- Thread-per-connection is simple, but doesn’t scale (each thread uses stack and OS resources).
- Async tasks are lightweight and scheduled efficiently by Tokio.

**Example:**
```rust
// Async functions return Future types
async fn handle_connection() -> Result<()> {
    // Await points where the function can yield control
    let stream = TcpStream::connect("127.0.0.1:8080").await?;
    Ok(())
}
```

### Ownership in Network Programming

Rust’s ownership model ensures that sockets and buffers are never used after being closed or moved. This prevents use-after-free and data race bugs common in C/C++ network code.

**Splitting Streams:**
You can split a `TcpStream` into read and write halves, allowing bidirectional forwarding in separate async tasks.

**Shared State:**
If you need to share state (e.g., connection counters), use `Arc<Mutex<T>>` for safe, concurrent access.

### Error Handling Patterns

Network code must handle many error cases: connection refused, timeouts, partial reads/writes, etc. Rust’s `Result` and error propagation make this robust and explicit.

**Design Decision:**
We propagate errors up and log them, rather than panicking or ignoring them. This is critical for production reliability.

```rust
// Async functions return Future types
async fn handle_connection() -> Result<()> {
    // Await points where the function can yield control
    let stream = TcpStream::connect("127.0.0.1:8080").await?;
    Ok(())
}
```

### Ownership in Network Programming

Network streams have unique ownership requirements:
- **Move Semantics**: Streams are moved between async tasks
- **Splitting**: Read/write halves can be used independently
- **Arc/Mutex**: Shared ownership when needed

### Error Handling Patterns

Network programming requires robust error handling:
- **Connection Errors**: Network unreachable, connection refused
- **I/O Errors**: Broken pipes, timeouts, partial reads
- **Resource Errors**: File descriptor limits, memory exhaustion

## Implementation: Basic TCP Proxy

Let's implement our TCP proxy step by step, starting with the core functionality.

### Creating the Proxy Module

First, let's set up the proxy module structure in `src/proxy/mod.rs`:

```rust
//! TCP and HTTP proxy implementations
//! 
//! This module provides the core proxying functionality, including
//! connection handling, data forwarding, and error management.

pub mod tcp_proxy;
pub mod http_proxy;

pub use tcp_proxy::TcpProxy;
pub use http_proxy::HttpProxy;

use std::net::SocketAddr;
use tokio::net::TcpStream;

/// Trait for proxy implementations
#[async_trait::async_trait]
pub trait Proxy {
    /// Handle an incoming connection
    async fn handle_connection(&self, stream: TcpStream, client_addr: SocketAddr) -> crate::Result<()>;
    
    /// Start the proxy server
    async fn start(&self, bind_addr: SocketAddr) -> crate::Result<()>;
}
```

### TCP Proxy Implementation

Now let's implement the core TCP proxy in `src/proxy/tcp_proxy.rs`:

```rust
use std::net::SocketAddr;
use tokio::net::{TcpListener, TcpStream};
use tokio::io::{self, AsyncReadExt, AsyncWriteExt};
use tracing::{info, warn, error, debug};
use crate::Result;

/// A basic TCP proxy that forwards connections to a backend server
pub struct TcpProxy {
    backend_addr: SocketAddr,
}

impl TcpProxy {
    /// Create a new TCP proxy
    pub fn new(backend_addr: SocketAddr) -> Self {
        Self { backend_addr }
    }
    
    /// Start the proxy server
    pub async fn start(&self, bind_addr: SocketAddr) -> Result<()> {
        let listener = TcpListener::bind(bind_addr).await?;
        info!("TCP Proxy listening on {}, forwarding to {}", bind_addr, self.backend_addr);
        
        loop {
            match listener.accept().await {
                Ok((client_stream, client_addr)) => {
                    debug!("New connection from {}", client_addr);
                    
                    // Clone backend address for the task
                    let backend_addr = self.backend_addr;
                    
                    // Spawn a new task to handle this connection
                    tokio::spawn(async move {
                        if let Err(e) = Self::handle_connection(client_stream, client_addr, backend_addr).await {
                            error!("Connection handling error: {}", e);
                        }
                    });
                }
                Err(e) => {
                    error!("Failed to accept connection: {}", e);
                }
            }
        }
    }
    
    /// Handle a single client connection
    async fn handle_connection(
        client_stream: TcpStream,
        client_addr: SocketAddr,
        backend_addr: SocketAddr,
    ) -> Result<()> {
        // Connect to the backend server
        let backend_stream = match TcpStream::connect(backend_addr).await {
            Ok(stream) => {
                debug!("Connected to backend {} for client {}", backend_addr, client_addr);
                stream
            }
            Err(e) => {
                error!("Failed to connect to backend {}: {}", backend_addr, e);
                return Err(e.into());
            }
        };
        
        // Forward data bidirectionally
        if let Err(e) = Self::forward_data(client_stream, backend_stream).await {
            warn!("Data forwarding error for client {}: {}", client_addr, e);
        }
        
        debug!("Connection closed for client {}", client_addr);
        Ok(())
    }
    
    /// Forward data bidirectionally between client and backend
    async fn forward_data(client_stream: TcpStream, backend_stream: TcpStream) -> Result<()> {
        // Split streams into read and write halves
        let (client_read, client_write) = client_stream.into_split();
        let (backend_read, backend_write) = backend_stream.into_split();
        
        // Forward data in both directions concurrently
        let client_to_backend = Self::copy_data(client_read, backend_write, "client->backend");
        let backend_to_client = Self::copy_data(backend_read, client_write, "backend->client");
        
        // Wait for either direction to complete or error
        tokio::select! {
            result = client_to_backend => {
                debug!("Client to backend forwarding completed: {:?}", result);
                result
            }
            result = backend_to_client => {
                debug!("Backend to client forwarding completed: {:?}", result);
                result
            }
        }
    }
    
    /// Copy data from reader to writer
    async fn copy_data<R, W>(
        mut reader: R,
        mut writer: W,
        direction: &str,
    ) -> Result<()>
    where
        R: AsyncReadExt + Unpin,
        W: AsyncWriteExt + Unpin,
    {
        let mut buffer = [0u8; 8192]; // 8KB buffer
        let mut total_bytes = 0u64;
        
        loop {
            match reader.read(&mut buffer).await {
                Ok(0) => {
                    // EOF reached
                    debug!("{}: EOF reached, {} bytes transferred", direction, total_bytes);
                    break;
                }
                Ok(n) => {
                    // Write the data
                    if let Err(e) = writer.write_all(&buffer[..n]).await {
                        error!("{}: Write error after {} bytes: {}", direction, total_bytes, e);
                        return Err(e.into());
                    }
                    
                    total_bytes += n as u64;
                    debug!("{}: Transferred {} bytes (total: {})", direction, n, total_bytes);
                }
                Err(e) => {
                    error!("{}: Read error after {} bytes: {}", direction, total_bytes, e);
                    return Err(e.into());
                }
            }
        }
        
        // Ensure all data is written
        writer.shutdown().await?;
        info!("{}: Transfer completed, {} bytes total", direction, total_bytes);
        Ok(())
    }
}
```

### Understanding the Implementation

Let's break down the key concepts and design decisions:

#### 1. Async Task Spawning
```rust
tokio::spawn(async move {
    // Handle connection in separate task
});
```
- Each connection runs in its own async task
- `move` captures variables by value
- Tasks run concurrently, enabling high throughput

#### 2. Stream Splitting
```rust
let (client_read, client_write) = client_stream.into_split();
```
- Splits TCP stream into independent read/write halves
- Enables simultaneous bidirectional data transfer
- Rust's ownership system ensures memory safety

#### 3. Concurrent Data Forwarding
```rust
tokio::select! {
    result = client_to_backend => result,
    result = backend_to_client => result,
}
```
- `select!` waits for the first future to complete
- When one direction closes, the entire connection closes
- Prevents resource leaks from half-open connections

## Testing the TCP Proxy

Let's create a simple test to verify our proxy works. First, update `src/main.rs`:

```rust
use rusty_balancer::proxy::TcpProxy;
use std::net::SocketAddr;
use tracing::info;

#[tokio::main]
async fn main() -> rusty_balancer::Result<()> {
    // Initialize logging
    tracing_subscriber::fmt::init();
    
    info!("Starting Rusty Balancer TCP Proxy");
    
    // Configuration (hardcoded for now)
    let bind_addr: SocketAddr = "127.0.0.1:8080".parse()?;
    let backend_addr: SocketAddr = "127.0.0.1:8081".parse()?;
    
    // Create and start the proxy
    let proxy = TcpProxy::new(backend_addr);
    proxy.start(bind_addr).await?;
    
    Ok(())
}
```

### Manual Testing

1. **Start a backend server** (using netcat):
```bash
# Terminal 1: Start a simple echo server
nc -l 8081
```

2. **Start the proxy**:
```bash
# Terminal 2: Start our proxy
RUST_LOG=debug cargo run
```

3. **Test the connection**:
```bash
# Terminal 3: Connect through the proxy
nc 127.0.0.1 8080
```

Type messages in Terminal 3 and see them appear in Terminal 1, and vice versa!

## Error Handling and Edge Cases

Our implementation handles several important scenarios:

### Connection Failures
- Backend server unavailable
- Network timeouts
- Connection refused

### Data Transfer Issues
- Partial reads/writes
- Connection drops mid-transfer
- Buffer overflow protection

### Resource Management
- Automatic cleanup when connections close
- Proper stream shutdown
- Memory-efficient buffering

## Performance Considerations

### Buffer Size Selection
```rust
let mut buffer = [0u8; 8192]; // 8KB buffer
```
- Larger buffers: Fewer system calls, more memory usage
- Smaller buffers: More system calls, less memory usage
- 8KB is a good balance for most use cases

### Connection Limits
- Operating system file descriptor limits
- Memory usage per connection
- CPU overhead of task switching

## Next Steps Preview

In Module 03, we'll enhance our proxy to:
- Parse HTTP requests and responses
- Add request routing capabilities
- Implement connection pooling
- Add basic load balancing logic

## Key Takeaways

- **Async Programming**: Essential for high-performance network applications
- **Ownership Model**: Rust's ownership prevents common networking bugs
- **Error Handling**: Network programming requires comprehensive error handling
- **Resource Management**: Proper cleanup prevents resource leaks
- **Concurrency**: Tokio's task system enables handling many connections efficiently

The TCP proxy we've built is the foundation for more advanced load balancing features. It demonstrates core concepts that apply to all network programming in Rust.

## Navigation
- [Previous: Project Setup and Architecture](01-project-setup.md)
- [Next: HTTP Protocol Implementation](03-http-protocol.md)
