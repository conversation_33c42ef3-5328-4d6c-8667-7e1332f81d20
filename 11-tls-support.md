
# Module 08: HTTP Load Balancing

## Learning Objectives
- Upgrade the proxy to parse and route HTTP requests
- Implement protocol-aware routing and header manipulation
- Support content-based routing and sticky sessions
- Understand the benefits and trade-offs of HTTP-aware load balancing

## Why HTTP Load Balancing?
HTTP is the foundation of most modern web services. By making the load balancer HTTP-aware, we can:
- Route based on URL paths, headers, cookies, or methods
- Implement sticky sessions and advanced routing logic
- Modify requests and responses (add headers, rewrite URLs)
- Enable protocol-specific optimizations (compression, caching)

## Implementation: HTTP-Aware Proxy

We'll use the `hyper` crate for HTTP parsing and proxying.

### Step 1: HTTP Proxy Structure

```rust
// src/proxy/http_proxy.rs
use hyper::{Body, Request, Response, StatusCode, Uri};
use hyper::client::HttpConnector;
use hyper::service::{make_service_fn, service_fn};
use hyper::{Client, Server};
use std::convert::Infallible;
use std::net::SocketAddr;
use std::sync::Arc;
use tracing::{info, warn, error, debug};
use crate::backend::BackendPool;

pub struct HttpProxy {
	backend_pool: Arc<BackendPool>,
	client: Client<HttpConnector>,
}

impl HttpProxy {
	pub fn new(backend_pool: Arc<BackendPool>) -> Self {
		Self {
			backend_pool,
			client: Client::new(),
		}
	}

	pub async fn start(&self, bind_addr: SocketAddr) -> crate::Result<()> {
		let backend_pool = Arc::clone(&self.backend_pool);
		let client = self.client.clone();
		let make_svc = make_service_fn(move |_conn| {
			let backend_pool = Arc::clone(&backend_pool);
			let client = client.clone();
			async move {
				Ok::<_, Infallible>(service_fn(move |req| {
					Self::handle_request(req, client.clone(), Arc::clone(&backend_pool))
				}))
			}
		});
		let server = Server::bind(&bind_addr).serve(make_svc);
		info!("HTTP Load Balancer listening on {}", bind_addr);
		if let Err(e) = server.await {
			error!("HTTP proxy server error: {}", e);
		}
		Ok(())
	}

	async fn handle_request(
		mut req: Request<Body>,
		client: Client<HttpConnector>,
		backend_pool: Arc<BackendPool>,
	) -> Result<Response<Body>, Infallible> {
		debug!("Handling request: {} {}", req.method(), req.uri());
		// Content-based routing example
		let backend_addr = match Self::select_backend(&req, &backend_pool) {
			Some(addr) => addr,
			None => {
				error!("No healthy backends available");
				return Ok(Self::error_response(StatusCode::SERVICE_UNAVAILABLE));
			}
		};
		debug!("Selected backend: {}", backend_addr);
		if let Err(e) = Self::prepare_backend_request(&mut req, backend_addr) {
			error!("Failed to prepare backend request: {}", e);
			return Ok(Self::error_response(StatusCode::BAD_GATEWAY));
		}
		match client.request(req).await {
			Ok(mut response) => {
				debug!("Backend {} responded with: {}", backend_addr, response.status());
				Self::prepare_client_response(&mut response);
				Ok(response)
			}
			Err(e) => {
				error!("Backend {} request failed: {}", backend_addr, e);
				Ok(Self::error_response(StatusCode::BAD_GATEWAY))
			}
		}
	}

	fn select_backend(req: &Request<Body>, backend_pool: &BackendPool) -> Option<SocketAddr> {
		// Example: route /api/* to one pool, /static/* to another, else round-robin
		let path = req.uri().path();
		// For now, use round-robin for all
		backend_pool.get_next_backend()
	}

	fn prepare_backend_request(req: &mut Request<Body>, backend_addr: SocketAddr) -> crate::Result<()> {
		let original_uri = req.uri();
		let new_uri = format!(
			"http://{}{}{}",
			backend_addr,
			original_uri.path(),
			original_uri.query().map(|q| format!("?{}", q)).unwrap_or_default()
		);
		*req.uri_mut() = new_uri.parse()?;
		let headers = req.headers_mut();
		headers.insert("host", format!("{}", backend_addr).parse().unwrap());
		headers.insert("x-forwarded-for", "127.0.0.1".parse().unwrap());
		headers.insert("x-forwarded-proto", "http".parse().unwrap());
		Ok(())
	}

	fn prepare_client_response(response: &mut Response<Body>) {
		let headers = response.headers_mut();
		headers.insert("x-proxy", "rusty-balancer".parse().unwrap());
		headers.remove("connection");
		headers.remove("upgrade");
		headers.remove("proxy-authenticate");
		headers.remove("proxy-authorization");
		headers.remove("te");
		headers.remove("trailers");
		headers.remove("transfer-encoding");
	}

	fn error_response(status: StatusCode) -> Response<Body> {
		Response::builder()
			.status(status)
			.header("content-type", "text/plain")
			.body(Body::from(format!("Proxy Error: {}", status)))
			.unwrap()
	}
}
```

### Step 2: Testing HTTP Load Balancing

1. Start multiple backend HTTP servers (e.g., with Python's `http.server`).
2. Start the load balancer and send requests to it.
3. Observe round-robin or content-based routing in action.

### Step 3: Advanced Features

- **Sticky Sessions:** Use cookies or client IP to route requests to the same backend.
- **Header Manipulation:** Add, remove, or rewrite headers for security or compliance.
- **Protocol Upgrades:** Support WebSockets or HTTP/2 as needed.

## Key Takeaways
- HTTP-aware load balancing enables smarter, content-based routing.
- Hyper provides efficient HTTP parsing and proxying in Rust.
- Header management and protocol awareness are essential for modern load balancers.
- Advanced features (sticky sessions, protocol upgrades) can be layered on top.

## Navigation
- [Previous: Health Checks](07-health-checks.md)
- [Next: Metrics and Monitoring](09-metrics-monitoring.md)
