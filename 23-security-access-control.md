# 17 - Plugin System

## Goal
Allow users to extend the load balancer with custom logic via plugins.

## Concepts Introduced
- Dynamic loading (e.g., with `libloading`)
- Defining plugin interfaces
- Safety and isolation

## Why a Plugin System?
It enables advanced customization without modifying core code.

## Alternatives Considered
- Static feature set (less flexible)
- Scripting (possible, but more complex)

## Next Steps
We'll enhance health checks with advanced options.
