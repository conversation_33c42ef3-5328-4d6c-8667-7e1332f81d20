# Module 07: Logging and Metrics

## Learning Objectives
- Implement structured logging with tracing
- Add metrics collection for monitoring
- Create Prometheus endpoint for metrics export
- Design meaningful dashboards for observability

## Prerequisites
- Completion of Module 06: Configuration System
- Understanding of logging and metrics concepts
- Familiarity with Prometheus and metrics formats

## Navigation
- [Previous: Configuration System](06-configuration-system.md)
- [Next: TLS and Security](08-tls-security.md)

---

## Logging Implementation

We'll use the `tracing` crate for structured logging:

```rust
use tracing::{info, warn, error, Level};
use tracing_subscriber::{FmtSubscriber, EnvFilter};

/// Initialize logging system
pub fn setup_logging() -> Result<()> {
    let subscriber = FmtSubscriber::builder()
        .with_env_filter(EnvFilter::from_default_env()
            .add_directive(Level::INFO.into())
            .add_directive("rusty_lb=debug".parse()?))
        .with_thread_ids(true)
        .with_thread_names(true)
        .with_file(true)
        .with_line_number(true)
        .with_target(false)
        .with_timer(tracing_subscriber::fmt::time::UtcTime::rfc_3339())
        .json()
        .flatten_event(true)
        .build();

    tracing::subscriber::set_global_default(subscriber)?;
    
    info!("Logging system initialized");
    Ok(())
}
```

---

## Metrics Collection

We'll use the `metrics` crate for collecting metrics and expose them in Prometheus format:

```rust
use metrics::{counter, gauge, histogram};
use metrics_exporter_prometheus::{Matcher, PrometheusBuilder, PrometheusHandle};
use std::time::Instant;

pub struct Metrics {
    prometheus: PrometheusHandle,
}

impl Metrics {
    pub fn new() -> Self {
        let builder = PrometheusBuilder::new();
        let prometheus = builder
            .with_global_prefix("rusty_lb")
            .set_buckets(&[0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0])
            .unwrap()
            .install_recorder()
            .unwrap();

        Self { prometheus }
    }

    /// Record request metrics
    pub fn record_request(&self, backend: &SocketAddr, status: u16, duration: f64) {
        // Total requests counter
        counter!("requests_total", 1, "backend" => backend.to_string());
        
        // Status code counter
        counter!("response_status", 1, 
            "backend" => backend.to_string(),
            "status" => status.to_string()
        );
        
        // Request duration histogram
        histogram!("request_duration_seconds", duration,
            "backend" => backend.to_string()
        );
    }

    /// Update backend health status
    pub fn update_backend_health(&self, backend: &SocketAddr, healthy: bool) {
        gauge!("backend_healthy", if healthy { 1.0 } else { 0.0 },
            "backend" => backend.to_string()
        );
    }

    /// Get metrics in Prometheus format
    pub fn get_metrics(&self) -> String {
        self.prometheus.render()
    }
}
```

---

## Request Tracking Middleware

Add a middleware to track request metrics:

```rust
pub struct MetricsMiddleware {
    metrics: Arc<Metrics>,
}

impl MetricsMiddleware {
    pub fn new(metrics: Arc<Metrics>) -> Self {
        Self { metrics }
    }

    pub async fn handle<B>(&self, req: Request<B>, backend: SocketAddr) -> Response<B> {
        let start = Instant::now();
        
        // Forward request to backend
        let response = self.next.call(req).await;
        
        // Record metrics
        let duration = start.elapsed().as_secs_f64();
        let status = response.status().as_u16();
        self.metrics.record_request(&backend, status, duration);
        
        response
    }
}
```

---

## Prometheus Endpoint

Create an endpoint to expose metrics:

```rust
async fn metrics_handler(metrics: Arc<Metrics>) -> Response<Body> {
    Response::builder()
        .header("content-type", "text/plain")
        .body(Body::from(metrics.get_metrics()))
        .unwrap()
}
```

---

## Example Metrics

Here are some key metrics we collect:

1. **Request Metrics**
   - Total requests per backend
   - Response status codes
   - Request duration histogram
   - Active connections

2. **Backend Health**
   - Health check status
   - Last check timestamp
   - Check duration

3. **System Metrics**
   - Memory usage
   - CPU usage
   - Open file descriptors
   - Goroutine count

---

## Grafana Dashboard

Example Grafana queries for visualizing metrics:

```
# Request Rate
rate(rusty_lb_requests_total[5m])

# Error Rate
rate(rusty_lb_response_status{status=~"5.*"}[5m])

# 95th Percentile Latency
histogram_quantile(0.95, rate(rusty_lb_request_duration_seconds_bucket[5m]))

# Backend Health
rusty_lb_backend_healthy
```

---

## Summary
- Structured logging with tracing
- Comprehensive metrics collection
- Prometheus integration
- Ready-to-use Grafana queries

---

## Next Steps
Continue to [Module 08: TLS and Security](08-tls-security.md) to add encryption and security features.
