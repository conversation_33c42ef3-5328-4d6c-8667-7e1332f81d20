# 14 - Sticky Sessions (Session Affinity)

## Goal
Implement session affinity so clients consistently connect to the same backend.

## Concepts Introduced
- Hashing and mapping clients
- Session tracking
- Trade-offs in affinity

## Why Sticky Sessions?
Some applications require clients to always hit the same backend (e.g., for in-memory sessions).

## Alternatives Considered
- Stateless load balancing (simpler, but not always possible)
- External session stores (adds complexity)

## Next Steps
We'll add rate limiting and DoS protection.
