# Module 04: Round-Robin Load Balancing

## Learning Objectives
- Understand the round-robin load balancing algorithm
- Implement a round-robin backend selector in Rust
- Integrate round-robin logic into the HTTP proxy
- Discuss trade-offs and failure scenarios

## Prerequisites
- Completion of Module 03: HTTP Protocol Implementation
- Familiarity with <PERSON><PERSON>'s concurrency primitives (Arc, Mutex)
- Understanding of basic load balancing concepts

## Navigation
- [Previous: HTTP Protocol Implementation](03-http-protocol.md)
- [Next: Health Checks and Backend Monitoring](05-health-checks.md)

---

## What is Round-Robin Load Balancing?

Round-robin is a simple, widely-used algorithm for distributing requests across a set of backend servers. Each incoming request is forwarded to the next backend in the list, cycling back to the start when the end is reached.

**Advantages:**
- Simple to implement
- Evenly distributes load (if all backends are healthy)
- No need for request state tracking

**Disadvantages:**
- Does not account for backend health or capacity
- Can send requests to failed or slow servers

---

## Implementation: Round-Robin Selector

We'll use <PERSON><PERSON>'s `Arc<Mutex<...>>` to safely share and update the backend index across async tasks.

```rust
use std::sync::{Arc, Mutex};
use std::net::SocketAddr;

/// Thread-safe round-robin backend selector
pub struct RoundRobin {
    backends: Vec<SocketAddr>,
    index: Mutex<usize>,
}

impl RoundRobin {
    pub fn new(backends: Vec<SocketAddr>) -> Self {
        Self {
            backends,
            index: Mutex::new(0),
        }
    }

    /// Get the next backend address
    pub fn next(&self) -> SocketAddr {
        let mut idx = self.index.lock().unwrap();
        let addr = self.backends[*idx];
        *idx = (*idx + 1) % self.backends.len();
        addr
    }
}
```

---

## Integrating with the HTTP Proxy

Update your proxy to use the `RoundRobin` selector instead of a single backend address. On each request, call `round_robin.next()` to select the backend.

**Example integration:**
```rust
// ...existing code...
let backend_addr = round_robin.next();
// ...pass backend_addr to the proxy logic...
```

---

## Handling Failures

Round-robin does not skip failed backends by default. In the next module, we'll add health checks to avoid routing to unhealthy servers.

---

## Summary
- Round-robin is a simple, stateless load balancing algorithm
- Easy to implement and integrate
- Does not handle backend failures (to be addressed next)

---

## Next Steps
Continue to [Module 05: Health Checks and Backend Monitoring](05-health-checks.md) to make your load balancer more robust.
