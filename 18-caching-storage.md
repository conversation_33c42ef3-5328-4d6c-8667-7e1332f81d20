# Module 18: Caching and Storage

## Learning Objectives
- Implement distributed caching
- Add response caching strategies
- Create cache invalidation mechanisms
- Implement persistent storage
- Add cache warming capabilities

## Prerequisites
- Completion of Module 17: Service Discovery
- Understanding of caching patterns
- Familiarity with storage systems
- Knowledge of cache invalidation strategies

## Navigation
- [Previous: Service Discovery](17-service-discovery.md)
- [Next: Rate Limiting](19-rate-limiting.md)

---

## Distributed Cache

Implement a distributed cache using Redis:

```rust
use redis::{Client, Commands, Connection};
use std::time::Duration;
use serde::{Serialize, Deserialize};

pub struct DistributedCache {
    client: Client,
    default_ttl: Duration,
}

#[derive(Serialize, Deserialize)]
struct CacheEntry {
    content: Vec<u8>,
    content_type: String,
    headers: Vec<(String, String)>,
    status: u16,
    created_at: u64,
}

impl DistributedCache {
    pub fn new(redis_url: &str, default_ttl: Duration) -> Result<Self> {
        let client = Client::open(redis_url)?;
        
        Ok(Self {
            client,
            default_ttl,
        })
    }

    pub async fn get(
        &self,
        key: &str,
    ) -> Result<Option<Response<Body>>> {
        let mut conn = self.get_connection()?;
        
        let data: Option<String> = conn.get(key)?;
        
        if let Some(data) = data {
            let entry: CacheEntry = serde_json::from_str(&data)?;
            
            // Check if entry is still valid
            let now = std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)?
                .as_secs();
                
            if now - entry.created_at <= self.default_ttl.as_secs() {
                // Build response from cache entry
                let mut response = Response::builder()
                    .status(entry.status)
                    .body(Body::from(entry.content))?;
                    
                // Add headers
                for (name, value) in entry.headers {
                    response.headers_mut().insert(
                        name.parse()?,
                        value.parse()?,
                    );
                }
                
                Ok(Some(response))
            } else {
                // Entry expired, remove it
                conn.del(key)?;
                Ok(None)
            }
        } else {
            Ok(None)
        }
    }

    pub async fn set(
        &self,
        key: &str,
        response: Response<Body>,
        ttl: Option<Duration>,
    ) -> Result<()> {
        let mut conn = self.get_connection()?;
        
        // Convert response to cache entry
        let body_bytes = hyper::body::to_bytes(response.into_body()).await?;
        
        let entry = CacheEntry {
            content: body_bytes.to_vec(),
            content_type: response
                .headers()
                .get("content-type")
                .and_then(|v| v.to_str().ok())
                .unwrap_or("application/octet-stream")
                .to_string(),
            headers: response
                .headers()
                .iter()
                .map(|(k, v)| {
                    (
                        k.to_string(),
                        v.to_str().unwrap_or("").to_string(),
                    )
                })
                .collect(),
            status: response.status().as_u16(),
            created_at: std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)?
                .as_secs(),
        };
        
        // Store in Redis
        let ttl = ttl.unwrap_or(self.default_ttl);
        conn.set_ex(
            key,
            serde_json::to_string(&entry)?,
            ttl.as_secs() as usize,
        )?;
        
        Ok(())
    }

    pub async fn invalidate(&self, key: &str) -> Result<()> {
        let mut conn = self.get_connection()?;
        conn.del(key)?;
        Ok(())
    }

    fn get_connection(&self) -> Result<Connection> {
        Ok(self.client.get_connection()?)
    }
}
```

---

## Caching Strategy

Implement intelligent caching strategies:

```rust
pub struct CacheStrategy {
    cache: Arc<DistributedCache>,
    rules: Vec<CacheRule>,
}

struct CacheRule {
    pattern: Regex,
    ttl: Duration,
    vary_headers: Vec<String>,
    conditions: Vec<CacheCondition>,
}

enum CacheCondition {
    Method(Method),
    Header {
        name: String,
        pattern: Regex,
    },
    QueryParam {
        name: String,
        pattern: Regex,
    },
}

impl CacheStrategy {
    pub fn new(cache: Arc<DistributedCache>) -> Self {
        Self {
            cache,
            rules: Vec::new(),
        }
    }

    pub fn add_rule(
        &mut self,
        pattern: &str,
        ttl: Duration,
        vary_headers: Vec<String>,
        conditions: Vec<CacheCondition>,
    ) -> Result<()> {
        self.rules.push(CacheRule {
            pattern: Regex::new(pattern)?,
            ttl,
            vary_headers,
            conditions,
        });
        Ok(())
    }

    pub async fn handle_request(
        &self,
        request: &Request<Body>,
    ) -> Result<Option<Response<Body>>> {
        // Find matching rule
        let rule = match self.find_matching_rule(request) {
            Some(rule) => rule,
            None => return Ok(None),
        };
        
        // Check conditions
        if !self.check_conditions(request, rule) {
            return Ok(None);
        }
        
        // Generate cache key
        let key = self.generate_cache_key(request, rule);
        
        // Try to get from cache
        self.cache.get(&key).await
    }

    pub async fn cache_response(
        &self,
        request: &Request<Body>,
        response: Response<Body>,
    ) -> Result<Response<Body>> {
        // Find matching rule
        let rule = match self.find_matching_rule(request) {
            Some(rule) => rule,
            None => return Ok(response),
        };
        
        // Check if response is cacheable
        if !self.is_cacheable(&response) {
            return Ok(response);
        }
        
        // Generate cache key
        let key = self.generate_cache_key(request, rule);
        
        // Store in cache
        self.cache
            .set(&key, response.clone(), Some(rule.ttl))
            .await?;
            
        Ok(response)
    }

    fn find_matching_rule<'a>(
        &'a self,
        request: &Request<Body>,
    ) -> Option<&'a CacheRule> {
        self.rules
            .iter()
            .find(|r| r.pattern.is_match(request.uri().path()))
    }

    fn check_conditions(
        &self,
        request: &Request<Body>,
        rule: &CacheRule,
    ) -> bool {
        rule.conditions.iter().all(|condition| {
            match condition {
                CacheCondition::Method(method) => {
                    request.method() == method
                }
                CacheCondition::Header { name, pattern } => {
                    request
                        .headers()
                        .get(name)
                        .and_then(|v| v.to_str().ok())
                        .map(|v| pattern.is_match(v))
                        .unwrap_or(false)
                }
                CacheCondition::QueryParam { name, pattern } => {
                    request
                        .uri()
                        .query()
                        .and_then(|q| {
                            url::form_urlencoded::parse(q.as_bytes())
                                .find(|(k, _)| k == name)
                        })
                        .map(|(_, v)| pattern.is_match(&v))
                        .unwrap_or(false)
                }
            }
        })
    }

    fn generate_cache_key(
        &self,
        request: &Request<Body>,
        rule: &CacheRule,
    ) -> String {
        let mut hasher = blake3::Hasher::new();
        
        // Add method and path
        hasher.update(request.method().as_str().as_bytes());
        hasher.update(request.uri().path().as_bytes());
        
        // Add vary headers
        for header in &rule.vary_headers {
            if let Some(value) = request.headers().get(header) {
                hasher.update(value.as_bytes());
            }
        }
        
        hex::encode(hasher.finalize().as_bytes())
    }

    fn is_cacheable(&self, response: &Response<Body>) -> bool {
        // Check status code
        if !response.status().is_success() {
            return false;
        }
        
        // Check cache control headers
        if let Some(cache_control) = response
            .headers()
            .get("cache-control")
        {
            if let Ok(value) = cache_control.to_str() {
                if value.contains("no-store")
                    || value.contains("private")
                {
                    return false;
                }
            }
        }
        
        true
    }
}
```

---

## Cache Invalidation

Implement cache invalidation patterns:

```rust
pub struct CacheInvalidator {
    cache: Arc<DistributedCache>,
    patterns: HashMap<String, Vec<InvalidationPattern>>,
}

struct InvalidationPattern {
    method: Method,
    path_pattern: Regex,
    related_patterns: Vec<Regex>,
}

impl CacheInvalidator {
    pub fn new(cache: Arc<DistributedCache>) -> Self {
        Self {
            cache,
            patterns: HashMap::new(),
        }
    }

    pub fn add_invalidation_pattern(
        &mut self,
        resource: &str,
        pattern: InvalidationPattern,
    ) {
        self.patterns
            .entry(resource.to_string())
            .or_default()
            .push(pattern);
    }

    pub async fn invalidate_on_request(
        &self,
        request: &Request<Body>,
    ) -> Result<()> {
        let path = request.uri().path();
        
        for patterns in self.patterns.values() {
            for pattern in patterns {
                if pattern.method == *request.method()
                    && pattern.path_pattern.is_match(path)
                {
                    // Invalidate related patterns
                    for related in &pattern.related_patterns {
                        let keys = self.find_matching_keys(related).await?;
                        for key in keys {
                            self.cache.invalidate(&key).await?;
                        }
                    }
                }
            }
        }
        
        Ok(())
    }

    async fn find_matching_keys(
        &self,
        pattern: &Regex,
    ) -> Result<Vec<String>> {
        // Implement key pattern matching using Redis SCAN
        todo!()
    }
}
```

---

## Cache Warming

Implement cache warming strategies:

```rust
pub struct CacheWarmer {
    cache: Arc<DistributedCache>,
    warmup_patterns: Vec<WarmupPattern>,
}

struct WarmupPattern {
    path: String,
    method: Method,
    headers: HashMap<String, String>,
    frequency: Duration,
}

impl CacheWarmer {
    pub fn new(cache: Arc<DistributedCache>) -> Self {
        Self {
            cache,
            warmup_patterns: Vec::new(),
        }
    }

    pub fn add_warmup_pattern(
        &mut self,
        pattern: WarmupPattern,
    ) {
        self.warmup_patterns.push(pattern);
    }

    pub async fn start_warming(&self) {
        for pattern in &self.warmup_patterns {
            let cache = self.cache.clone();
            let pattern = pattern.clone();
            
            tokio::spawn(async move {
                let mut interval = tokio::time::interval(
                    pattern.frequency,
                );
                
                loop {
                    interval.tick().await;
                    
                    if let Err(e) = Self::warm_pattern(
                        &cache,
                        &pattern,
                    ).await {
                        error!("Cache warming error: {}", e);
                    }
                }
            });
        }
    }

    async fn warm_pattern(
        cache: &DistributedCache,
        pattern: &WarmupPattern,
    ) -> Result<()> {
        // Create request
        let mut request = Request::builder()
            .method(&pattern.method)
            .uri(&pattern.path)
            .body(Body::empty())?;
            
        // Add headers
        for (name, value) in &pattern.headers {
            request.headers_mut().insert(
                name.parse()?,
                value.parse()?,
            );
        }
        
        // Execute request
        let client = Client::new();
        let response = client.request(request).await?;
        
        // Cache response
        let key = Self::generate_cache_key(&pattern.path);
        cache.set(&key, response, None).await?;
        
        Ok(())
    }

    fn generate_cache_key(path: &str) -> String {
        use blake3::Hash;
        
        let hash = blake3::hash(path.as_bytes());
        hex::encode(hash.as_bytes())
    }
}
```

---

## Summary
- Implemented distributed caching with Redis
- Added intelligent caching strategies
- Created cache invalidation patterns
- Implemented cache warming
- Added persistent storage support

---

## Next Steps
Continue to [Module 19: Rate Limiting](19-rate-limiting.md) to learn about implementing rate limiting strategies.
