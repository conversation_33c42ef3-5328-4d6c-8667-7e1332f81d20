# Module 11: Traffic Management

## Learning Objectives
- Implement traffic shaping and rate limiting
- Add circuit breaker patterns
- Create traffic splitting and canary deployments
- Implement request retries and timeouts

## Prerequisites
- Completion of Module 10: High Availability
- Understanding of traffic control patterns
- Familiarity with circuit breaker concepts
- Knowledge of retry strategies

## Navigation
- [Previous: High Availability](10-high-availability.md)
- [Next: Request Transformation](12-request-transformation.md)

---

## Traffic Shaping

Implement token bucket rate limiting with burst allowance:

```rust
use std::sync::Arc;
use tokio::sync::Mutex;
use std::collections::HashMap;
use std::time::{Instant, Duration};

pub struct TrafficShaper {
    // Requests per second
    rate: f64,
    // Maximum burst size
    burst_size: u32,
    // Per-client token buckets
    buckets: Arc<Mutex<HashMap<String, TokenBucket>>>,
}

struct TokenBucket {
    tokens: f64,
    last_update: Instant,
    rate: f64,
    capacity: f64,
}

impl TrafficShaper {
    pub fn new(rate: f64, burst_size: u32) -> Self {
        Self {
            rate,
            burst_size,
            buckets: Arc::new(Mutex::new(HashMap::new())),
        }
    }

    pub async fn check_rate_limit(&self, client_id: &str) -> bool {
        let mut buckets = self.buckets.lock().await;
        let now = Instant::now();

        let bucket = buckets.entry(client_id.to_string()).or_insert_with(|| {
            TokenBucket {
                tokens: self.burst_size as f64,
                last_update: now,
                rate: self.rate,
                capacity: self.burst_size as f64,
            }
        });

        bucket.refill(now);

        if bucket.tokens >= 1.0 {
            bucket.tokens -= 1.0;
            true
        } else {
            false
        }
    }
}

impl TokenBucket {
    fn refill(&mut self, now: Instant) {
        let elapsed = now.duration_since(self.last_update).as_secs_f64();
        self.tokens = (self.tokens + elapsed * self.rate).min(self.capacity);
        self.last_update = now;
    }
}
```

---

## Circuit Breaker

Implement the circuit breaker pattern to handle backend failures:

```rust
use std::sync::atomic::{AtomicUsize, AtomicBool, Ordering};

pub struct CircuitBreaker {
    // Circuit state
    open: AtomicBool,
    // Failure counters
    failures: AtomicUsize,
    // Circuit configuration
    threshold: usize,
    reset_timeout: Duration,
}

impl CircuitBreaker {
    pub fn new(threshold: usize, reset_timeout: Duration) -> Self {
        Self {
            open: AtomicBool::new(false),
            failures: AtomicUsize::new(0),
            threshold,
            reset_timeout,
        }
    }

    pub async fn execute<F, T, E>(&self, f: F) -> Result<T, Error>
    where
        F: Future<Output = Result<T, E>>,
        E: std::error::Error,
    {
        if self.is_open() {
            return Err(Error::CircuitOpen);
        }

        match f.await {
            Ok(result) => {
                self.record_success();
                Ok(result)
            }
            Err(e) => {
                self.record_failure();
                Err(Error::BackendError(e.to_string()))
            }
        }
    }

    fn is_open(&self) -> bool {
        self.open.load(Ordering::SeqCst)
    }

    fn record_success(&self) {
        self.failures.store(0, Ordering::SeqCst);
        if self.is_open() {
            self.open.store(false, Ordering::SeqCst);
        }
    }

    fn record_failure(&self) {
        let failures = self.failures.fetch_add(1, Ordering::SeqCst) + 1;
        if failures >= self.threshold {
            self.trip();
        }
    }

    fn trip(&self) {
        self.open.store(true, Ordering::SeqCst);
        
        let reset_timeout = self.reset_timeout;
        let breaker = self.clone();
        
        tokio::spawn(async move {
            tokio::time::sleep(reset_timeout).await;
            breaker.half_open();
        });
    }

    fn half_open(&self) {
        self.failures.store(0, Ordering::SeqCst);
        self.open.store(false, Ordering::SeqCst);
    }
}
```

---

## Traffic Splitting

Implement traffic splitting for canary deployments:

```rust
pub struct TrafficSplitter {
    production: Vec<Backend>,
    canary: Vec<Backend>,
    canary_percentage: u32,
}

impl TrafficSplitter {
    pub fn new(
        production: Vec<Backend>,
        canary: Vec<Backend>,
        canary_percentage: u32,
    ) -> Self {
        assert!(canary_percentage <= 100);
        Self {
            production,
            canary,
            canary_percentage,
        }
    }

    pub fn select_backend(&self, request: &Request<Body>) -> &Backend {
        if self.should_use_canary(request) {
            self.select_from_canary()
        } else {
            self.select_from_production()
        }
    }

    fn should_use_canary(&self, request: &Request<Body>) -> bool {
        let hash = self.hash_request(request);
        (hash % 100) < self.canary_percentage
    }

    fn hash_request(&self, request: &Request<Body>) -> u32 {
        use std::hash::{Hash, Hasher};
        let mut hasher = DefaultHasher::new();
        
        // Hash consistent request attributes
        request.uri().path().hash(&mut hasher);
        if let Some(addr) = request.extensions().get::<SocketAddr>() {
            addr.hash(&mut hasher);
        }
        
        hasher.finish() as u32
    }
}
```

---

## Request Retries

Implement intelligent retry logic:

```rust
pub struct RetryPolicy {
    max_retries: u32,
    base_delay: Duration,
    max_delay: Duration,
    retryable_status_codes: HashSet<StatusCode>,
}

impl RetryPolicy {
    pub async fn execute<F, T, E>(&self, f: F) -> Result<T, Error>
    where
        F: Fn() -> Future<Output = Result<T, E>>,
        E: std::error::Error,
    {
        let mut attempts = 0;
        let mut delay = self.base_delay;

        loop {
            match f().await {
                Ok(result) => return Ok(result),
                Err(e) => {
                    attempts += 1;
                    if attempts >= self.max_retries {
                        return Err(Error::MaxRetriesExceeded(e.to_string()));
                    }

                    if !self.should_retry(&e) {
                        return Err(Error::NonRetryableError(e.to_string()));
                    }

                    // Exponential backoff with jitter
                    delay = self.calculate_next_delay(delay);
                    tokio::time::sleep(delay).await;
                }
            }
        }
    }

    fn should_retry(&self, error: &impl std::error::Error) -> bool {
        // Check if error type is retryable
        if let Some(status) = error.downcast_ref::<StatusCode>() {
            return self.retryable_status_codes.contains(status);
        }
        
        // Check for network errors
        error.is::<std::io::Error>() || error.is::<hyper::Error>()
    }

    fn calculate_next_delay(&self, current_delay: Duration) -> Duration {
        let next_delay = current_delay * 2;
        if next_delay > self.max_delay {
            self.max_delay
        } else {
            // Add jitter
            let jitter = rand::thread_rng().gen_range(0..=100);
            next_delay + Duration::from_millis(jitter)
        }
    }
}
```

---

## Integration

Combine all traffic management features:

```rust
pub struct TrafficManager {
    shaper: TrafficShaper,
    circuit_breaker: CircuitBreaker,
    splitter: TrafficSplitter,
    retry_policy: RetryPolicy,
}

impl TrafficManager {
    pub async fn handle_request(
        &self,
        request: Request<Body>,
    ) -> Result<Response<Body>> {
        // Check rate limit
        if !self.shaper.check_rate_limit(
            request.extensions().get::<ClientId>().unwrap()
        ).await {
            return Ok(Response::builder()
                .status(StatusCode::TOO_MANY_REQUESTS)
                .body(Body::empty())?);
        }

        // Select backend using traffic splitter
        let backend = self.splitter.select_backend(&request);

        // Execute request with circuit breaker and retries
        self.retry_policy
            .execute(|| {
                self.circuit_breaker
                    .execute(|| self.send_request(backend, request.clone()))
            })
            .await
    }

    async fn send_request(
        &self,
        backend: &Backend,
        request: Request<Body>,
    ) -> Result<Response<Body>> {
        // Implement request forwarding logic
        todo!()
    }
}
```

---

## Monitoring and Metrics

Add metrics for traffic management features:

```rust
impl TrafficManager {
    fn record_metrics(
        &self,
        backend: &Backend,
        response: &Response<Body>,
        duration: Duration,
    ) {
        // Record rate limiting metrics
        metrics::counter!("rate_limited_requests_total",
            "backend" => backend.addr.to_string());
            
        // Record circuit breaker metrics
        metrics::gauge!("circuit_breaker_state",
            if self.circuit_breaker.is_open() { 1.0 } else { 0.0 },
            "backend" => backend.addr.to_string());
            
        // Record retry metrics
        metrics::histogram!("request_retries",
            "backend" => backend.addr.to_string());
            
        // Record traffic split metrics
        metrics::counter!("requests_total",
            "backend" => backend.addr.to_string(),
            "deployment" => if backend.is_canary { "canary" } else { "production" });
    }
}
```

---

## Summary
- Implemented token bucket rate limiting
- Added circuit breaker pattern
- Created traffic splitting for canary deployments
- Implemented intelligent retry logic
- Added comprehensive metrics

---

## Next Steps
Continue to [Module 12: Request Transformation](12-request-transformation.md) to learn about modifying requests and responses.
