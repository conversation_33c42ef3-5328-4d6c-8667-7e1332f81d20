# Module 12: Request Transformation

## Learning Objectives
- Implement request and response header modification
- Add URL rewriting capabilities
- Create request/response body transformation
- Implement content compression

## Prerequisites
- Completion of Module 11: Traffic Management
- Understanding of HTTP headers and body formats
- Familiarity with content encoding
- Knowledge of middleware patterns

## Navigation
- [Previous: Traffic Management](11-traffic-management.md)
- [Next: Observability](13-observability.md)

---

## Header Transformation

Implement header modification middleware:

```rust
use http::{HeaderMap, HeaderName, HeaderValue};
use std::collections::HashMap;

pub struct HeaderTransformer {
    add_request_headers: HashMap<HeaderName, HeaderValue>,
    remove_request_headers: Vec<HeaderName>,
    add_response_headers: HashMap<HeaderName, HeaderValue>,
    remove_response_headers: Vec<HeaderName>,
}

impl HeaderTransformer {
    pub fn new() -> Self {
        Self {
            add_request_headers: HashMap::new(),
            remove_request_headers: Vec::new(),
            add_response_headers: HashMap::new(),
            remove_response_headers: Vec::new(),
        }
    }

    pub fn transform_request_headers(&self, headers: &mut HeaderMap) {
        // Remove specified headers
        for name in &self.remove_request_headers {
            headers.remove(name);
        }

        // Add new headers
        for (name, value) in &self.add_request_headers {
            headers.insert(name, value.clone());
        }
    }

    pub fn transform_response_headers(&self, headers: &mut HeaderMap) {
        // Remove specified headers
        for name in &self.remove_response_headers {
            headers.remove(name);
        }

        // Add new headers
        for (name, value) in &self.add_response_headers {
            headers.insert(name, value.clone());
        }
    }
}
```

---

## URL Rewriting

Implement URL rewriting rules:

```rust
use regex::Regex;
use url::Url;

pub struct UrlRewriter {
    rules: Vec<RewriteRule>,
}

struct RewriteRule {
    pattern: Regex,
    replacement: String,
    flags: RewriteFlags,
}

bitflags! {
    struct RewriteFlags: u8 {
        const LAST = 0b00000001;
        const REDIRECT = 0b00000010;
        const PERMANENT = 0b00000100;
    }
}

impl UrlRewriter {
    pub fn new(rules: Vec<RewriteRule>) -> Self {
        Self { rules }
    }

    pub fn rewrite_url(&self, url: &str) -> RewriteResult {
        let mut current_url = url.to_string();
        
        for rule in &self.rules {
            if let Some(new_url) = rule.apply(&current_url) {
                current_url = new_url;
                
                if rule.flags.contains(RewriteFlags::REDIRECT) {
                    return RewriteResult::Redirect {
                        url: current_url,
                        permanent: rule.flags.contains(RewriteFlags::PERMANENT),
                    };
                }
                
                if rule.flags.contains(RewriteFlags::LAST) {
                    break;
                }
            }
        }
        
        RewriteResult::Rewritten(current_url)
    }
}

enum RewriteResult {
    Rewritten(String),
    Redirect { url: String, permanent: bool },
}

impl RewriteRule {
    pub fn apply(&self, url: &str) -> Option<String> {
        self.pattern.replace(url, &self.replacement).into()
    }
}
```

---

## Body Transformation

Implement request/response body transformation:

```rust
use hyper::Body;
use serde_json::Value;
use bytes::Bytes;

pub struct BodyTransformer {
    request_transforms: Vec<Box<dyn Transform>>,
    response_transforms: Vec<Box<dyn Transform>>,
}

#[async_trait]
pub trait Transform: Send + Sync {
    async fn transform(&self, body: Bytes) -> Result<Bytes, Error>;
}

impl BodyTransformer {
    pub async fn transform_request(&self, body: Body) -> Result<Body, Error> {
        let bytes = hyper::body::to_bytes(body).await?;
        
        let mut current = bytes;
        for transform in &self.request_transforms {
            current = transform.transform(current).await?;
        }
        
        Ok(Body::from(current))
    }

    pub async fn transform_response(&self, body: Body) -> Result<Body, Error> {
        let bytes = hyper::body::to_bytes(body).await?;
        
        let mut current = bytes;
        for transform in &self.response_transforms {
            current = transform.transform(current).await?;
        }
        
        Ok(Body::from(current))
    }
}

// Example JSON transformation
pub struct JsonTransform {
    path: String,
    value: Value,
}

#[async_trait]
impl Transform for JsonTransform {
    async fn transform(&self, body: Bytes) -> Result<Bytes, Error> {
        let mut json: Value = serde_json::from_slice(&body)?;
        
        if let Some(value) = json.pointer_mut(&self.path) {
            *value = self.value.clone();
        }
        
        Ok(serde_json::to_vec(&json)?.into())
    }
}
```

---

## Content Compression

Implement response compression:

```rust
use async_compression::tokio::write::{GzipEncoder, BrotliEncoder, DeflateEncoder};
use tokio::io::AsyncWriteExt;

pub struct CompressionHandler {
    min_size: usize,
    compression_level: i32,
}

impl CompressionHandler {
    pub async fn compress_response(
        &self,
        mut response: Response<Body>,
    ) -> Result<Response<Body>, Error> {
        let headers = response.headers();
        
        // Check if compression is appropriate
        if !self.should_compress(headers) {
            return Ok(response);
        }

        // Get accepted encodings
        let accepts = self.get_accepted_encodings(headers);
        
        // Choose best compression method
        let encoding = self.choose_encoding(&accepts);
        
        if let Some(encoding) = encoding {
            // Compress body
            let body = hyper::body::to_bytes(response.body_mut()).await?;
            
            if body.len() >= self.min_size {
                let compressed = self.compress_body(body, encoding).await?;
                
                // Update headers
                response.headers_mut().insert(
                    "content-encoding",
                    HeaderValue::from_static(encoding.as_str()),
                );
                
                *response.body_mut() = Body::from(compressed);
            }
        }
        
        Ok(response)
    }

    async fn compress_body(
        &self,
        body: Bytes,
        encoding: CompressionType,
    ) -> Result<Bytes, Error> {
        let mut buf = Vec::new();
        
        match encoding {
            CompressionType::Gzip => {
                let mut encoder = GzipEncoder::with_quality(
                    &mut buf,
                    self.compression_level,
                );
                encoder.write_all(&body).await?;
                encoder.shutdown().await?;
            }
            CompressionType::Brotli => {
                let mut encoder = BrotliEncoder::with_quality(
                    &mut buf,
                    self.compression_level,
                );
                encoder.write_all(&body).await?;
                encoder.shutdown().await?;
            }
            CompressionType::Deflate => {
                let mut encoder = DeflateEncoder::with_quality(
                    &mut buf,
                    self.compression_level,
                );
                encoder.write_all(&body).await?;
                encoder.shutdown().await?;
            }
        }
        
        Ok(buf.into())
    }
}

#[derive(Debug, Clone, Copy)]
enum CompressionType {
    Gzip,
    Brotli,
    Deflate,
}

impl CompressionType {
    fn as_str(&self) -> &'static str {
        match self {
            CompressionType::Gzip => "gzip",
            CompressionType::Brotli => "br",
            CompressionType::Deflate => "deflate",
        }
    }
}
```

---

## Integration

Combine all transformation features:

```rust
pub struct TransformationLayer {
    header_transformer: HeaderTransformer,
    url_rewriter: UrlRewriter,
    body_transformer: BodyTransformer,
    compression_handler: CompressionHandler,
}

impl TransformationLayer {
    pub async fn transform_request(
        &self,
        mut request: Request<Body>,
    ) -> Result<Request<Body>, Error> {
        // Transform headers
        self.header_transformer.transform_request_headers(
            request.headers_mut(),
        );
        
        // Rewrite URL
        let uri = request.uri().to_string();
        match self.url_rewriter.rewrite_url(&uri) {
            RewriteResult::Rewritten(new_url) => {
                *request.uri_mut() = new_url.parse()?;
            }
            RewriteResult::Redirect { url, permanent } => {
                return Ok(Response::builder()
                    .status(if permanent { 301 } else { 302 })
                    .header("location", url)
                    .body(Body::empty())?
                    .into());
            }
        }
        
        // Transform body
        let new_body = self.body_transformer
            .transform_request(request.into_body())
            .await?;
        
        Ok(request.map(|_| new_body))
    }

    pub async fn transform_response(
        &self,
        mut response: Response<Body>,
    ) -> Result<Response<Body>, Error> {
        // Transform headers
        self.header_transformer.transform_response_headers(
            response.headers_mut(),
        );
        
        // Transform body
        let new_body = self.body_transformer
            .transform_response(response.into_body())
            .await?;
        
        let mut response = response.map(|_| new_body);
        
        // Apply compression
        response = self.compression_handler
            .compress_response(response)
            .await?;
        
        Ok(response)
    }
}
```

---

## Summary
- Implemented header transformation
- Added URL rewriting
- Created body transformation pipeline
- Implemented content compression
- Combined features in a transformation layer

---

## Next Steps
Continue to [Module 13: Observability](13-observability.md) to learn about monitoring and debugging capabilities.
