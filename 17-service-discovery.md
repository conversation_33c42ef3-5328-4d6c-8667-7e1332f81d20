# Module 17: Service Discovery

## Learning Objectives
- Implement service registration and discovery
- Add integration with service registries
- Create dynamic backend updates
- Implement health-based service routing
- Add service mesh integration

## Prerequisites
- Completion of Module 16: API Gateway Features
- Understanding of service discovery patterns
- Familiarity with service registries
- Knowledge of service mesh concepts

## Navigation
- [Previous: API Gateway Features](16-api-gateway.md)
- [Next: Caching and Storage](18-caching-storage.md)

---

## Service Registry Integration

Implement service registry client:

```rust
use async_trait::async_trait;
use std::collections::HashMap;
use std::net::SocketAddr;
use tokio::sync::RwLock;

#[async_trait]
pub trait ServiceRegistry: Send + Sync {
    async fn register_service(
        &self,
        service: &ServiceInstance,
    ) -> Result<(), Error>;
    
    async fn deregister_service(
        &self,
        service_id: &str,
    ) -> Result<(), Error>;
    
    async fn get_services(
        &self,
        service_name: &str,
    ) -> Result<Vec<ServiceInstance>, Error>;
    
    async fn watch_services(
        &self,
        service_name: &str,
    ) -> Result<ServiceWatcher, Error>;
}

pub struct ServiceInstance {
    id: String,
    name: String,
    address: SocketAddr,
    metadata: HashMap<String, String>,
    health_check: HealthCheck,
}

pub struct ConsulRegistry {
    client: consul::Client,
    services: RwLock<HashMap<String, ServiceInstance>>,
}

impl ConsulRegistry {
    pub async fn new(address: &str) -> Result<Self> {
        let client = consul::Client::new(address)?;
        
        Ok(Self {
            client,
            services: RwLock::new(HashMap::new()),
        })
    }
}

#[async_trait]
impl ServiceRegistry for ConsulRegistry {
    async fn register_service(
        &self,
        service: &ServiceInstance,
    ) -> Result<(), Error> {
        let registration = consul::AgentServiceRegistration {
            id: Some(service.id.clone()),
            name: service.name.clone(),
            address: Some(service.address.ip().to_string()),
            port: Some(service.address.port()),
            tags: Some(vec![]),
            meta: Some(service.metadata.clone()),
            check: Some(consul::AgentServiceCheck {
                http: Some(format!(
                    "http://{}:{}/health",
                    service.address.ip(),
                    service.address.port()
                )),
                interval: Some("10s".to_string()),
                timeout: Some("5s".to_string()),
                ..Default::default()
            }),
        };
        
        self.client
            .agent()
            .service_register(registration)
            .await?;
            
        self.services
            .write()
            .await
            .insert(service.id.clone(), service.clone());
            
        Ok(())
    }

    async fn deregister_service(
        &self,
        service_id: &str,
    ) -> Result<(), Error> {
        self.client
            .agent()
            .service_deregister(service_id)
            .await?;
            
        self.services
            .write()
            .await
            .remove(service_id);
            
        Ok(())
    }

    async fn get_services(
        &self,
        service_name: &str,
    ) -> Result<Vec<ServiceInstance>, Error> {
        let services = self.client
            .catalog()
            .service(service_name, None)
            .await?;
            
        Ok(services
            .into_iter()
            .filter_map(|s| {
                let address = format!("{}:{}", s.address, s.port)
                    .parse()
                    .ok()?;
                    
                Some(ServiceInstance {
                    id: s.id,
                    name: s.service,
                    address,
                    metadata: s.meta,
                    health_check: HealthCheck::Http {
                        path: "/health".to_string(),
                        interval: Duration::from_secs(10),
                        timeout: Duration::from_secs(5),
                    },
                })
            })
            .collect())
    }

    async fn watch_services(
        &self,
        service_name: &str,
    ) -> Result<ServiceWatcher, Error> {
        let (tx, rx) = tokio::sync::watch::channel(Vec::new());
        
        let client = self.client.clone();
        let service_name = service_name.to_string();
        
        tokio::spawn(async move {
            let mut index = "0".to_string();
            
            loop {
                match client
                    .catalog()
                    .service_wait(&service_name, None, &index)
                    .await
                {
                    Ok((new_index, services)) => {
                        index = new_index;
                        let instances = services
                            .into_iter()
                            .filter_map(|s| {
                                let address = format!(
                                    "{}:{}",
                                    s.address,
                                    s.port
                                ).parse().ok()?;
                                
                                Some(ServiceInstance {
                                    id: s.id,
                                    name: s.service,
                                    address,
                                    metadata: s.meta,
                                    health_check: HealthCheck::Http {
                                        path: "/health".to_string(),
                                        interval: Duration::from_secs(10),
                                        timeout: Duration::from_secs(5),
                                    },
                                })
                            })
                            .collect();
                            
                        let _ = tx.send(instances);
                    }
                    Err(e) => {
                        error!("Service watch error: {}", e);
                        tokio::time::sleep(
                            Duration::from_secs(5)
                        ).await;
                    }
                }
            }
        });
        
        Ok(ServiceWatcher { rx })
    }
}
```

---

## Dynamic Backend Updates

Implement dynamic backend management:

```rust
pub struct DynamicBackendManager {
    registry: Box<dyn ServiceRegistry>,
    backend_pool: Arc<RwLock<BackendPool>>,
}

impl DynamicBackendManager {
    pub fn new(
        registry: Box<dyn ServiceRegistry>,
    ) -> Self {
        Self {
            registry,
            backend_pool: Arc::new(RwLock::new(BackendPool::new())),
        }
    }

    pub async fn start_service_discovery(
        &self,
        service_name: &str,
    ) -> Result<(), Error> {
        let watcher = self.registry
            .watch_services(service_name)
            .await?;
            
        let backend_pool = self.backend_pool.clone();
        
        tokio::spawn(async move {
            while let Ok(services) = watcher.rx.changed().await {
                let services = services.borrow().clone();
                
                let mut pool = backend_pool.write().await;
                pool.update_backends(
                    services
                        .into_iter()
                        .map(|s| Backend::new(s.address))
                        .collect(),
                );
            }
        });
        
        Ok(())
    }

    pub async fn get_backend(
        &self,
        request: &Request<Body>,
    ) -> Result<Backend, Error> {
        let pool = self.backend_pool.read().await;
        
        pool.select_backend(request)
            .ok_or(Error::NoAvailableBackends)
    }
}

pub struct BackendPool {
    backends: Vec<Backend>,
    selector: Box<dyn BackendSelector>,
}

impl BackendPool {
    pub fn new() -> Self {
        Self {
            backends: Vec::new(),
            selector: Box::new(RoundRobinSelector::new()),
        }
    }

    pub fn update_backends(&mut self, new_backends: Vec<Backend>) {
        self.backends = new_backends;
    }

    pub fn select_backend(
        &self,
        request: &Request<Body>,
    ) -> Option<Backend> {
        self.selector.select(&self.backends, request)
    }
}
```

---

## Health-Based Routing

Implement health-aware service selection:

```rust
pub struct HealthAwareSelector {
    health_checks: Arc<HealthChecker>,
}

impl HealthAwareSelector {
    pub fn new(health_checks: Arc<HealthChecker>) -> Self {
        Self { health_checks }
    }
}

#[async_trait]
impl BackendSelector for HealthAwareSelector {
    async fn select(
        &self,
        backends: &[Backend],
        _request: &Request<Body>,
    ) -> Option<Backend> {
        // Filter healthy backends
        let healthy_backends: Vec<_> = backends
            .iter()
            .filter(|b| self.health_checks.is_healthy(b))
            .collect();
            
        if healthy_backends.is_empty() {
            return None;
        }
        
        // Use weighted round-robin among healthy backends
        let total_weight: u32 = healthy_backends
            .iter()
            .map(|b| b.weight())
            .sum();
            
        let mut selected_weight = 
            rand::thread_rng().gen_range(0..total_weight);
            
        for backend in healthy_backends {
            if selected_weight < backend.weight() {
                return Some(backend.clone());
            }
            selected_weight -= backend.weight();
        }
        
        None
    }
}
```

---

## Service Mesh Integration

Implement service mesh proxy integration:

```rust
pub struct ServiceMeshProxy {
    mesh_client: MeshClient,
    service_name: String,
}

impl ServiceMeshProxy {
    pub fn new(
        mesh_endpoint: &str,
        service_name: String,
    ) -> Result<Self> {
        let mesh_client = MeshClient::connect(mesh_endpoint)?;
        
        Ok(Self {
            mesh_client,
            service_name,
        })
    }

    pub async fn proxy_request(
        &self,
        mut request: Request<Body>,
    ) -> Result<Response<Body>> {
        // Add service mesh headers
        let headers = request.headers_mut();
        headers.insert(
            "x-service-mesh-id",
            self.mesh_client.get_mesh_id().parse()?,
        );
        
        headers.insert(
            "x-destination-service",
            self.service_name.parse()?,
        );
        
        // Get service instance from mesh
        let instance = self.mesh_client
            .resolve_service(&self.service_name)
            .await?;
            
        // Add routing headers
        headers.insert(
            "x-route-version",
            instance.version.parse()?,
        );
        
        // Forward request through mesh
        self.mesh_client
            .forward_request(instance, request)
            .await
    }

    pub async fn register_metrics(&self) -> Result<()> {
        let metrics = self.collect_metrics().await?;
        
        self.mesh_client
            .report_metrics(metrics)
            .await
    }

    async fn collect_metrics(&self) -> Result<Vec<Metric>> {
        // Collect proxy metrics
        let mut metrics = Vec::new();
        
        // Request count
        metrics.push(Metric {
            name: "requests_total".to_string(),
            value: self.get_request_count().await?,
            labels: HashMap::from([
                ("service".to_string(), self.service_name.clone()),
            ]),
        });
        
        // Latency
        metrics.push(Metric {
            name: "request_latency".to_string(),
            value: self.get_average_latency().await?,
            labels: HashMap::from([
                ("service".to_string(), self.service_name.clone()),
            ]),
        });
        
        Ok(metrics)
    }
}
```

---

## Summary
- Implemented service registry integration
- Added dynamic backend updates
- Created health-based routing
- Implemented service mesh integration
- Added metrics collection

---

## Next Steps
Continue to [Module 18: Caching and Storage](18-caching-storage.md) to learn about implementing caching strategies.
