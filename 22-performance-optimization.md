# Module 25: Performance Optimization

## Learning Objectives
- Implement connection pooling
- Add request pipelining
- Create efficient caching
- Implement async I/O
- Add performance monitoring

## Prerequisites
- Completion of Module 24: Logging and Analytics
- Understanding of performance optimization
- Knowledge of async programming
- Familiarity with profiling tools

## Navigation
- [Previous: Logging and Analytics](24-logging-analytics.md)
- [Next: High Availability](26-high-availability.md)

---

## Connection Pooling

Implement connection pooling:

```rust
use std::collections::VecDeque;
use std::sync::Arc;
use tokio::sync::{Mutex, Semaphore};
use tokio::time::{Duration, Instant};
use hyper::client::conn::SendRequest;

pub struct ConnectionPool {
    connections: Arc<Mutex<VecDeque<PooledConnection>>>,
    semaphore: Arc<Semaphore>,
    config: PoolConfig,
}

struct PooledConnection {
    sender: SendRequest<hyper::Body>,
    last_used: Instant,
    uses: u32,
}

#[derive(Clone)]
pub struct PoolConfig {
    max_size: usize,
    min_idle: usize,
    max_lifetime: Duration,
    max_uses: u32,
    connect_timeout: Duration,
}

impl ConnectionPool {
    pub fn new(config: PoolConfig) -> Self {
        Self {
            connections: Arc::new(Mutex::new(VecDeque::new())),
            semaphore: Arc::new(Semaphore::new(config.max_size)),
            config,
        }
    }

    pub async fn get_connection(
        &self,
        uri: &hyper::Uri,
    ) -> Result<PooledConnection, Box<dyn std::error::Error>> {
        // Acquire semaphore permit
        let _permit = self.semaphore.acquire().await?;
        
        // Try to get existing connection
        let mut connections = self.connections.lock().await;
        
        while let Some(conn) = connections.pop_front() {
            // Check if connection is still valid
            if self.is_connection_valid(&conn) {
                return Ok(conn);
            }
        }
        
        // Create new connection
        let connection = self
            .create_connection(uri)
            .await?;
            
        Ok(connection)
    }

    pub async fn release_connection(
        &self,
        mut conn: PooledConnection,
    ) {
        let mut connections = self.connections.lock().await;
        
        // Update connection stats
        conn.last_used = Instant::now();
        conn.uses += 1;
        
        // Check if connection should be reused
        if self.is_connection_valid(&conn) {
            connections.push_back(conn);
        } else {
            // Connection will be dropped and semaphore permit released
        }
    }

    fn is_connection_valid(&self, conn: &PooledConnection) -> bool {
        let now = Instant::now();
        
        // Check lifetime
        if now.duration_since(conn.last_used)
            > self.config.max_lifetime
        {
            return false;
        }
        
        // Check uses
        if conn.uses >= self.config.max_uses {
            return false;
        }
        
        true
    }

    async fn create_connection(
        &self,
        uri: &hyper::Uri,
    ) -> Result<PooledConnection, Box<dyn std::error::Error>> {
        let stream = tokio::time::timeout(
            self.config.connect_timeout,
            tokio::net::TcpStream::connect((
                uri.host().unwrap(),
                uri.port_u16().unwrap_or(80),
            )),
        )
        .await??;
        
        let (sender, connection) = hyper::client::conn::handshake(stream)
            .await?;
            
        // Spawn connection driver
        tokio::spawn(async move {
            if let Err(e) = connection.await {
                error!("Connection error: {}", e);
            }
        });
        
        Ok(PooledConnection {
            sender,
            last_used: Instant::now(),
            uses: 0,
        })
    }

    pub async fn maintain_pool(self: Arc<Self>) {
        let mut interval = tokio::time::interval(
            Duration::from_secs(30),
        );
        
        loop {
            interval.tick().await;
            self.cleanup_idle_connections().await;
            self.ensure_minimum_connections().await;
        }
    }

    async fn cleanup_idle_connections(&self) {
        let mut connections = self.connections.lock().await;
        let now = Instant::now();
        
        connections.retain(|conn| {
            now.duration_since(conn.last_used)
                <= self.config.max_lifetime
        });
    }

    async fn ensure_minimum_connections(&self) {
        let connections = self.connections.lock().await;
        let current_size = connections.len();
        drop(connections);
        
        if current_size < self.config.min_idle {
            for _ in current_size..self.config.min_idle {
                if let Err(e) = self
                    .create_connection(&hyper::Uri::from_static("http://localhost"))
                    .await
                {
                    error!("Failed to create idle connection: {}", e);
                    break;
                }
            }
        }
    }
}
```

---

## Request Pipelining

Implement request pipelining:

```rust
use futures::stream::{FuturesUnordered, StreamExt};
use std::future::Future;
use tokio::sync::mpsc;

pub struct RequestPipeline {
    sender: mpsc::Sender<PipelineRequest>,
    config: PipelineConfig,
}

struct PipelineRequest {
    request: hyper::Request<hyper::Body>,
    response_tx: oneshot::Sender<
        Result<hyper::Response<hyper::Body>, Box<dyn std::error::Error>>,
    >,
}

#[derive(Clone)]
pub struct PipelineConfig {
    max_concurrent_requests: usize,
    request_timeout: Duration,
    max_retries: u32,
}

impl RequestPipeline {
    pub fn new(
        config: PipelineConfig,
        pool: Arc<ConnectionPool>,
    ) -> Self {
        let (tx, rx) = mpsc::channel(config.max_concurrent_requests);
        
        // Spawn pipeline processor
        tokio::spawn(process_pipeline(rx, pool, config.clone()));
        
        Self {
            sender: tx,
            config,
        }
    }

    pub async fn send_request(
        &self,
        request: hyper::Request<hyper::Body>,
    ) -> Result<hyper::Response<hyper::Body>, Box<dyn std::error::Error>> {
        let (response_tx, response_rx) = oneshot::channel();
        
        self.sender
            .send(PipelineRequest {
                request,
                response_tx,
            })
            .await
            .map_err(|e| {
                Box::new(std::io::Error::new(
                    std::io::ErrorKind::Other,
                    e.to_string(),
                ))
            })?;
            
        tokio::time::timeout(
            self.config.request_timeout,
            response_rx,
        )
        .await
        .map_err(|_| "Request timeout".into())?
        .map_err(|e| "Pipeline error".into())?
    }
}

async fn process_pipeline(
    mut rx: mpsc::Receiver<PipelineRequest>,
    pool: Arc<ConnectionPool>,
    config: PipelineConfig,
) {
    let mut in_flight = FuturesUnordered::new();
    
    while let Some(request) = rx.recv().await {
        // Add request to pipeline
        in_flight.push(process_request(
            request,
            pool.clone(),
            config.clone(),
        ));
        
        // Process completed requests
        while let Some(()) = in_flight.next().await {
            // Continue processing
        }
        
        // Limit concurrent requests
        while in_flight.len() >= config.max_concurrent_requests {
            in_flight.next().await;
        }
    }
}

async fn process_request(
    request: PipelineRequest,
    pool: Arc<ConnectionPool>,
    config: PipelineConfig,
) {
    let mut retries = 0;
    let mut last_error = None;
    
    while retries < config.max_retries {
        match send_request_with_connection(
            request.request.clone(),
            &pool,
        )
        .await
        {
            Ok(response) => {
                let _ = request.response_tx.send(Ok(response));
                return;
            }
            Err(e) => {
                last_error = Some(e);
                retries += 1;
                tokio::time::sleep(
                    Duration::from_millis(100 * 2u64.pow(retries)),
                )
                .await;
            }
        }
    }
    
    let _ = request.response_tx.send(Err(last_error.unwrap()));
}

async fn send_request_with_connection(
    request: hyper::Request<hyper::Body>,
    pool: &ConnectionPool,
) -> Result<hyper::Response<hyper::Body>, Box<dyn std::error::Error>> {
    let uri = request.uri().clone();
    let mut conn = pool.get_connection(&uri).await?;
    
    let response = conn.sender.send_request(request).await?;
    
    pool.release_connection(conn).await;
    
    Ok(response)
}
```

---

## Efficient Caching

Implement efficient caching:

```rust
use lru::LruCache;
use std::hash::{Hash, Hasher};
use std::num::NonZeroUsize;
use tokio::sync::RwLock;

pub struct CacheKey {
    method: hyper::Method,
    uri: hyper::Uri,
    headers: Vec<(String, String)>,
}

impl Hash for CacheKey {
    fn hash<H: Hasher>(&self, state: &mut H) {
        self.method.hash(state);
        self.uri.hash(state);
        for (name, value) in &self.headers {
            name.hash(state);
            value.hash(state);
        }
    }
}

pub struct CacheEntry {
    response: hyper::Response<hyper::Body>,
    created_at: Instant,
    ttl: Duration,
}

pub struct Cache {
    storage: RwLock<LruCache<CacheKey, CacheEntry>>,
    config: CacheConfig,
}

#[derive(Clone)]
pub struct CacheConfig {
    max_size: NonZeroUsize,
    default_ttl: Duration,
    vary_headers: Vec<String>,
}

impl Cache {
    pub fn new(config: CacheConfig) -> Self {
        Self {
            storage: RwLock::new(
                LruCache::new(config.max_size),
            ),
            config,
        }
    }

    pub async fn get(
        &self,
        request: &hyper::Request<hyper::Body>,
    ) -> Option<hyper::Response<hyper::Body>> {
        let key = self.create_cache_key(request);
        let mut cache = self.storage.write().await;
        
        if let Some(entry) = cache.get(&key) {
            if self.is_entry_valid(entry) {
                Some(entry.response.clone())
            } else {
                cache.pop(&key);
                None
            }
        } else {
            None
        }
    }

    pub async fn set(
        &self,
        request: &hyper::Request<hyper::Body>,
        response: hyper::Response<hyper::Body>,
        ttl: Option<Duration>,
    ) {
        if !self.is_cacheable(&response) {
            return;
        }
        
        let key = self.create_cache_key(request);
        let entry = CacheEntry {
            response,
            created_at: Instant::now(),
            ttl: ttl.unwrap_or(self.config.default_ttl),
        };
        
        let mut cache = self.storage.write().await;
        cache.put(key, entry);
    }

    fn create_cache_key(
        &self,
        request: &hyper::Request<hyper::Body>,
    ) -> CacheKey {
        let mut vary_headers = Vec::new();
        
        for header_name in &self.config.vary_headers {
            if let Some(value) = request
                .headers()
                .get(header_name)
            {
                vary_headers.push((
                    header_name.clone(),
                    value.to_str()
                        .unwrap_or("")
                        .to_string(),
                ));
            }
        }
        
        CacheKey {
            method: request.method().clone(),
            uri: request.uri().clone(),
            headers: vary_headers,
        }
    }

    fn is_cacheable(
        &self,
        response: &hyper::Response<hyper::Body>,
    ) -> bool {
        // Check status code
        if !response.status().is_success() {
            return false;
        }
        
        // Check cache control headers
        if let Some(cache_control) = response
            .headers()
            .get(hyper::header::CACHE_CONTROL)
        {
            let value = cache_control
                .to_str()
                .unwrap_or("");
            
            if value.contains("no-store")
                || value.contains("private")
            {
                return false;
            }
        }
        
        true
    }

    fn is_entry_valid(&self, entry: &CacheEntry) -> bool {
        entry.created_at.elapsed() < entry.ttl
    }

    pub async fn maintain_cache(self: Arc<Self>) {
        let mut interval = tokio::time::interval(
            Duration::from_secs(60),
        );
        
        loop {
            interval.tick().await;
            self.cleanup_expired().await;
        }
    }

    async fn cleanup_expired(&self) {
        let mut cache = self.storage.write().await;
        let now = Instant::now();
        
        cache.retain(|_, entry| {
            now.duration_since(entry.created_at) < entry.ttl
        });
    }
}
```

---

## Async I/O

Optimize async I/O operations:

```rust
use tokio::io::{AsyncRead, AsyncWrite};
use tokio::net::TcpStream;
use tokio_util::codec::{Decoder, Encoder};
use bytes::{BytesMut, BufMut};

pub struct OptimizedConnection<T>
where
    T: AsyncRead + AsyncWrite + Unpin,
{
    stream: T,
    read_buffer: BytesMut,
    write_buffer: BytesMut,
    max_buffer_size: usize,
}

impl<T> OptimizedConnection<T>
where
    T: AsyncRead + AsyncWrite + Unpin,
{
    pub fn new(
        stream: T,
        max_buffer_size: usize,
    ) -> Self {
        Self {
            stream,
            read_buffer: BytesMut::with_capacity(4096),
            write_buffer: BytesMut::with_capacity(4096),
            max_buffer_size,
        }
    }

    pub async fn read_frame(
        &mut self,
    ) -> Result<Option<BytesMut>, std::io::Error> {
        loop {
            // Check if we have a complete frame
            if let Some(frame) = self.parse_frame()? {
                return Ok(Some(frame));
            }
            
            // Check buffer capacity
            if self.read_buffer.len() >= self.max_buffer_size {
                return Err(std::io::Error::new(
                    std::io::ErrorKind::Other,
                    "buffer overflow",
                ));
            }
            
            // Read more data
            let bytes_read = self.stream
                .read_buf(&mut self.read_buffer)
                .await?;
                
            if bytes_read == 0 {
                return if self.read_buffer.is_empty() {
                    Ok(None)
                } else {
                    Err(std::io::Error::new(
                        std::io::ErrorKind::UnexpectedEof,
                        "connection closed",
                    ))
                };
            }
        }
    }

    pub async fn write_frame(
        &mut self,
        frame: BytesMut,
    ) -> Result<(), std::io::Error> {
        // Check buffer capacity
        if self.write_buffer.len() + frame.len()
            > self.max_buffer_size
        {
            self.flush().await?;
        }
        
        // Add frame to buffer
        self.write_buffer.extend_from_slice(&frame);
        
        // Flush if buffer is getting full
        if self.write_buffer.len()
            >= self.max_buffer_size / 2
        {
            self.flush().await?;
        }
        
        Ok(())
    }

    pub async fn flush(
        &mut self,
    ) -> Result<(), std::io::Error> {
        while !self.write_buffer.is_empty() {
            let bytes_written = self.stream
                .write_buf(&mut self.write_buffer)
                .await?;
                
            if bytes_written == 0 {
                return Err(std::io::Error::new(
                    std::io::ErrorKind::WriteZero,
                    "failed to write to socket",
                ));
            }
        }
        
        self.stream.flush().await
    }

    fn parse_frame(
        &mut self,
    ) -> Result<Option<BytesMut>, std::io::Error> {
        // Implement frame parsing logic
        // This depends on your protocol
        todo!()
    }
}

pub struct AsyncPool<T>
where
    T: AsyncRead + AsyncWrite + Unpin,
{
    connections: Arc<Mutex<VecDeque<OptimizedConnection<T>>>>,
    config: PoolConfig,
}

impl<T> AsyncPool<T>
where
    T: AsyncRead + AsyncWrite + Unpin,
{
    pub fn new(config: PoolConfig) -> Self {
        Self {
            connections: Arc::new(Mutex::new(VecDeque::new())),
            config,
        }
    }

    pub async fn get_connection(
        &self,
    ) -> Result<OptimizedConnection<T>, std::io::Error> {
        let mut connections = self.connections.lock().await;
        
        if let Some(conn) = connections.pop_front() {
            Ok(conn)
        } else {
            Err(std::io::Error::new(
                std::io::ErrorKind::Other,
                "no connections available",
            ))
        }
    }

    pub async fn release_connection(
        &self,
        conn: OptimizedConnection<T>,
    ) {
        let mut connections = self.connections.lock().await;
        connections.push_back(conn);
    }
}
```

---

## Performance Monitoring

Implement performance monitoring:

```rust
use metrics::{Counter, Gauge, Histogram};
use std::time::Instant;

pub struct PerformanceMonitor {
    request_latency: Histogram,
    active_connections: Gauge,
    bytes_transmitted: Counter,
    connection_errors: Counter,
    cache_hits: Counter,
    cache_misses: Counter,
}

impl PerformanceMonitor {
    pub fn new() -> Self {
        Self {
            request_latency: Histogram::new("request_latency"),
            active_connections: Gauge::new("active_connections"),
            bytes_transmitted: Counter::new("bytes_transmitted"),
            connection_errors: Counter::new("connection_errors"),
            cache_hits: Counter::new("cache_hits"),
            cache_misses: Counter::new("cache_misses"),
        }
    }

    pub fn track_request<F, Fut, T>(
        &self,
        f: F,
    ) -> impl Future<Output = T>
    where
        F: FnOnce() -> Fut,
        Fut: Future<Output = T>,
    {
        let start = Instant::now();
        self.active_connections.inc();
        
        async move {
            let result = f().await;
            self.active_connections.dec();
            self.request_latency
                .record(start.elapsed().as_secs_f64());
            result
        }
    }

    pub fn record_bytes_transmitted(&self, bytes: u64) {
        self.bytes_transmitted.inc_by(bytes);
    }

    pub fn record_connection_error(&self) {
        self.connection_errors.inc();
    }

    pub fn record_cache_hit(&self) {
        self.cache_hits.inc();
    }

    pub fn record_cache_miss(&self) {
        self.cache_misses.inc();
    }

    pub fn get_metrics(&self) -> PerformanceMetrics {
        PerformanceMetrics {
            avg_latency: self.request_latency.mean(),
            p95_latency: self.request_latency.percentile(95.0),
            p99_latency: self.request_latency.percentile(99.0),
            active_connections: self.active_connections.get(),
            total_bytes: self.bytes_transmitted.get(),
            error_count: self.connection_errors.get(),
            cache_hit_ratio: self.calculate_cache_hit_ratio(),
        }
    }

    fn calculate_cache_hit_ratio(&self) -> f64 {
        let hits = self.cache_hits.get();
        let misses = self.cache_misses.get();
        let total = hits + misses;
        
        if total == 0 {
            0.0
        } else {
            hits as f64 / total as f64
        }
    }
}

#[derive(Debug, Clone)]
pub struct PerformanceMetrics {
    pub avg_latency: f64,
    pub p95_latency: f64,
    pub p99_latency: f64,
    pub active_connections: i64,
    pub total_bytes: u64,
    pub error_count: u64,
    pub cache_hit_ratio: f64,
}
```

---

## Summary
- Implemented connection pooling
- Added request pipelining
- Created efficient caching
- Implemented optimized async I/O
- Added performance monitoring

---

## Next Steps
Continue to [Module 26: High Availability](26-high-availability.md) to learn about implementing high availability features.
