# Module 03: HTTP Protocol Implementation

## Learning Objectives
- Understand HTTP protocol structure and parsing requirements
- Implement HTTP request and response parsing using Hyper
- Learn about HTTP connection management and keep-alive
- Build an HTTP-aware proxy with header manipulation capabilities
- Understand the differences between HTTP/1.1 and HTTP/2
- Implement proper HTTP error handling and status codes

## Prerequisites
- Completion of Module 02: Basic TCP Proxy
- Understanding of HTTP protocol basics (requests, responses, headers)
- Familiarity with <PERSON><PERSON>'s trait system and generic programming
- Knowledge of async/await patterns and error handling

## Navigation
- [Previous: Basic TCP Proxy](02-basic-tcp-proxy.md)
- [Next: Round-Robin Load Balancing](04-round-robin-balancing.md)

## HTTP Protocol Fundamentals

HTTP (HyperText Transfer Protocol) is the foundation of web communication. Unlike our TCP proxy that blindly forwards bytes, an HTTP proxy understands the structure of HTTP messages and can make intelligent routing decisions.

### HTTP Message Structure

```
HTTP Request:
GET /api/users HTTP/1.1
Host: example.com
User-Agent: Mozilla/5.0
Content-Length: 0

[Optional Body]

HTTP Response:
HTTP/1.1 200 OK
Content-Type: application/json
Content-Length: 25

{"users": ["alice", "bob"]}
```

### Why HTTP-Aware Load Balancing?

**Layer 7 Benefits**:
- Route based on URL paths, headers, or cookies
- Modify requests/responses (add headers, rewrite URLs)
- Implement sticky sessions
- Cache responses
- Compress content
- SSL termination

**Trade-offs**:
- Higher CPU usage (parsing overhead)
- More memory usage (buffering requests)
- Increased latency (processing time)
- More complex implementation

### HTTP/1.1 vs HTTP/2

| Feature                | HTTP/1.1                     | HTTP/2                       |
|------------------------|------------------------------|------------------------------|
| Connection Management | One request per connection   | Multiplexing over a single connection |
| Header Compression    | None                         | HPACK compression            |
| Prioritization        | None                         | Stream prioritization        |
| Performance           | Slower due to head-of-line blocking | Faster due to multiplexing |

HTTP/2 significantly improves performance by allowing multiple requests and responses to be sent simultaneously over a single connection. This reduces latency and improves resource utilization, making it ideal for modern web applications.

## Rust HTTP Ecosystem

### Hyper: The Foundation

Hyper is Rust's most popular HTTP library, providing:
- Fast HTTP/1.1 and HTTP/2 implementation
- Client and server components
- Async/await support
- Zero-copy parsing where possible

### Key Types

```rust
use hyper::{Request, Response, Body, Method, Uri, StatusCode};
use hyper::header::{HeaderMap, HeaderName, HeaderValue};

// HTTP request type
type HttpRequest = Request<Body>;

// HTTP response type  
type HttpResponse = Response<Body>;
```

## Implementation: HTTP Proxy

Let's implement an HTTP-aware proxy that can parse requests and make routing decisions.

### HTTP Proxy Structure

Create `src/proxy/http_proxy.rs`:

```rust
use hyper::{Body, Request, Response, StatusCode, Uri};
use hyper::client::HttpConnector;
use hyper::service::{make_service_fn, service_fn};
use hyper::{Client, Server};
use std::convert::Infallible;
use std::net::SocketAddr;
use tracing::{info, warn, error, debug};
use crate::Result;

/// HTTP-aware proxy that can parse and modify requests
pub struct HttpProxy {
    backend_addr: SocketAddr,
    client: Client<HttpConnector>,
}

impl HttpProxy {
    /// Create a new HTTP proxy
    pub fn new(backend_addr: SocketAddr) -> Self {
        let client = Client::new();
        Self {
            backend_addr,
            client,
        }
    }
    
    /// Start the HTTP proxy server
    pub async fn start(&self, bind_addr: SocketAddr) -> Result<()> {
        // Clone for move into service
        let backend_addr = self.backend_addr;
        let client = self.client.clone();
        
        // Create service factory
        let make_svc = make_service_fn(move |_conn| {
            let client = client.clone();
            let backend_addr = backend_addr;
            
            async move {
                Ok::<_, Infallible>(service_fn(move |req| {
                    Self::handle_request(req, client.clone(), backend_addr)
                }))
            }
        });
        
        // Start server
        let server = Server::bind(&bind_addr).serve(make_svc);
        info!("HTTP Proxy listening on {}, forwarding to {}", bind_addr, backend_addr);
        
        if let Err(e) = server.await {
            error!("HTTP proxy server error: {}", e);
        }
        
        Ok(())
    }
    
    /// Handle a single HTTP request
    async fn handle_request(
        mut req: Request<Body>,
        client: Client<HttpConnector>,
        backend_addr: SocketAddr,
    ) -> Result<Response<Body>, Infallible> {
        debug!("Handling request: {} {}", req.method(), req.uri());
        
        // Modify request for backend
        match Self::prepare_backend_request(&mut req, backend_addr) {
            Ok(_) => {}
            Err(e) => {
                error!("Failed to prepare backend request: {}", e);
                return Ok(Self::error_response(StatusCode::BAD_GATEWAY));
            }
        }
        
        // Forward request to backend
        match client.request(req).await {
            Ok(mut response) => {
                debug!("Backend response: {}", response.status());
                
                // Modify response headers if needed
                Self::prepare_client_response(&mut response);
                
                Ok(response)
            }
            Err(e) => {
                error!("Backend request failed: {}", e);
                Ok(Self::error_response(StatusCode::BAD_GATEWAY))
            }
        }
    }
    
    /// Prepare request for forwarding to backend
    fn prepare_backend_request(req: &mut Request<Body>, backend_addr: SocketAddr) -> Result<()> {
        // Build new URI with backend address
        let original_uri = req.uri();
        let new_uri = format!(
            "http://{}{}{}",
            backend_addr,
            original_uri.path(),
            original_uri.query().map(|q| format!("?{}", q)).unwrap_or_default()
        );
        
        *req.uri_mut() = new_uri.parse().map_err(|e| {
            error!("Invalid URI: {}", e);
            e
        })?;
        
        // Add/modify headers
        let headers = req.headers_mut();
        
        // Update Host header
        headers.insert("host", format!("{}", backend_addr).parse().unwrap());
        
        // Add proxy headers
        headers.insert("x-forwarded-for", "127.0.0.1".parse().unwrap());
        headers.insert("x-forwarded-proto", "http".parse().unwrap());
        
        debug!("Modified request URI: {}", req.uri());
        Ok(())
    }
    
    /// Prepare response for client
    fn prepare_client_response(response: &mut Response<Body>) {
        let headers = response.headers_mut();
        
        // Add proxy identification
        headers.insert("x-proxy", "rusty-balancer".parse().unwrap());
        
        // Remove hop-by-hop headers
        headers.remove("connection");
        headers.remove("upgrade");
        headers.remove("proxy-authenticate");
        headers.remove("proxy-authorization");
        headers.remove("te");
        headers.remove("trailers");
        headers.remove("transfer-encoding");
    }
    
    /// Create error response
    fn error_response(status: StatusCode) -> Response<Body> {
        Response::builder()
            .status(status)
            .header("content-type", "text/plain")
            .body(Body::from(format!("Proxy Error: {}", status)))
            .unwrap()
    }
}
```

### Understanding HTTP Proxy Components

#### 1. Service Architecture

The proxy uses Hyper's `make_service_fn` and `service_fn` to create a service for each incoming connection. This allows us to handle requests asynchronously and efficiently.

#### 2. Request Preparation

The `prepare_backend_request` function modifies the incoming request to route it to the backend server. This includes updating the URI and adding proxy headers like `x-forwarded-for`.

#### 3. Response Preparation

The `prepare_client_response` function modifies the backend's response before sending it to the client. This includes adding proxy identification headers and removing hop-by-hop headers.

#### 4. Error Handling

The `error_response` function generates appropriate HTTP error responses for the client, ensuring a consistent user experience.

### Performance Considerations

- **Connection Pooling**: Use connection pooling to reduce the overhead of creating new connections.
- **Header Compression**: Leverage HTTP/2's header compression to reduce bandwidth usage.
- **Load Testing**: Perform load testing to identify bottlenecks and optimize performance.
- **Tracing**: Use tracing tools to monitor and debug the proxy's performance.

By understanding and implementing these components, you can build a robust and efficient HTTP-aware proxy.

## Advanced HTTP Features

### Connection Pooling

HTTP/1.1 supports connection reuse through keep-alive:

```rust
use hyper::client::HttpConnector;
use hyper::Client;

// Client automatically pools connections
let client = Client::builder()
    .pool_idle_timeout(Duration::from_secs(30))
    .pool_max_idle_per_host(10)
    .build::<_, hyper::Body>(HttpConnector::new());
```

### Request/Response Body Handling

```rust
use hyper::Body;
use bytes::Bytes;

async fn read_body(body: Body) -> Result<Bytes> {
    hyper::body::to_bytes(body).await.map_err(Into::into)
}

async fn modify_request_body(req: &mut Request<Body>) -> Result<()> {
    let body_bytes = hyper::body::to_bytes(req.body_mut()).await?;
    
    // Modify body content
    let modified = modify_content(body_bytes);
    
    // Replace body
    *req.body_mut() = Body::from(modified);
    Ok(())
}
```

### Content-Based Routing

```rust
impl HttpProxy {
    async fn route_request(&self, req: &Request<Body>) -> SocketAddr {
        match req.uri().path() {
            path if path.starts_with("/api/") => self.api_backend,
            path if path.starts_with("/static/") => self.static_backend,
            _ => self.default_backend,
        }
    }
}
```

## Testing the HTTP Proxy

Update `src/main.rs` to test our HTTP proxy:

```rust
use rusty_balancer::proxy::HttpProxy;
use std::net::SocketAddr;
use tracing::info;

#[tokio::main]
async fn main() -> rusty_balancer::Result<()> {
    tracing_subscriber::fmt::init();
    
    info!("Starting Rusty Balancer HTTP Proxy");
    
    let bind_addr: SocketAddr = "127.0.0.1:8080".parse()?;
    let backend_addr: SocketAddr = "127.0.0.1:8081".parse()?;
    
    let proxy = HttpProxy::new(backend_addr);
    proxy.start(bind_addr).await?;
    
    Ok(())
}
```

### Testing Setup

1. **Start a backend HTTP server**:
```bash
# Simple Python HTTP server
python3 -m http.server 8081
```

2. **Start the proxy**:
```bash
RUST_LOG=debug cargo run
```

3. **Test with curl**:
```bash
curl -v http://127.0.0.1:8080/
curl -H "Custom-Header: test" http://127.0.0.1:8080/
```

## HTTP/2 Considerations

HTTP/2 introduces additional complexity:
- **Binary Protocol**: Not human-readable like HTTP/1.1
- **Multiplexing**: Multiple requests per connection
- **Server Push**: Server can send resources proactively
- **Header Compression**: HPACK compression algorithm

Hyper supports HTTP/2, but it requires additional configuration:

```rust
use hyper::server::conn::Http;

let http = Http::new().http2_only(true);
```

## Error Handling Patterns

HTTP proxies must handle various error scenarios:

```rust
impl HttpProxy {
    fn handle_backend_error(error: hyper::Error) -> Response<Body> {
        match error {
            e if e.is_connect() => {
                Self::error_response(StatusCode::BAD_GATEWAY)
            }
            e if e.is_timeout() => {
                Self::error_response(StatusCode::GATEWAY_TIMEOUT)
            }
            _ => {
                Self::error_response(StatusCode::INTERNAL_SERVER_ERROR)
            }
        }
    }
}
```

## Performance Optimization

### Zero-Copy Operations
```rust
// Avoid copying body data when possible
let body = req.into_body();
let response = Response::new(body);
```

### Connection Reuse
```rust
// Configure client for optimal connection pooling
let client = Client::builder()
    .pool_idle_timeout(Duration::from_secs(30))
    .pool_max_idle_per_host(20)
    .build::<_, Body>(HttpConnector::new());
```

## Next Steps Preview

In Module 04, we'll add load balancing logic:
- Multiple backend servers
- Round-robin algorithm implementation
- Backend server health tracking
- Request distribution strategies

## Key Takeaways

- **HTTP Parsing**: Hyper provides efficient HTTP parsing and generation
- **Service Architecture**: Hyper's service model enables clean request handling
- **Header Management**: Proper header handling is crucial for HTTP proxies
- **Error Handling**: HTTP proxies must handle various network and protocol errors
- **Performance**: Connection pooling and zero-copy operations improve efficiency

Our HTTP proxy now understands the application layer, enabling intelligent routing decisions based on request content.

## Navigation
- [Previous: Basic TCP Proxy](02-basic-tcp-proxy.md)
- [Next: Round-Robin Load Balancing](04-round-robin-balancing.md)
