# Module 24: Logging and Analytics

## Learning Objectives
- Implement structured logging
- Add analytics collection
- Create log aggregation
- Implement metrics dashboards
- Add reporting features

## Prerequisites
- Completion of Module 23: DDoS Protection
- Understanding of logging systems
- Knowledge of analytics platforms
- Familiarity with metrics visualization

## Navigation
- [Previous: DDoS Protection](23-ddos-protection.md)
- [Next: Performance Optimization](25-performance-optimization.md)

---

## Structured Logging

Implement structured logging:

```rust
use serde::Serialize;
use std::sync::Arc;
use tracing::{
    Event, Level, Subscriber,
    field::{Field, Visit},
    span::{Attributes, Record},
};
use tracing_subscriber::{
    fmt::format::JsonFields,
    layer::SubscriberExt,
    Registry,
};

#[derive(Debug, Serialize)]
struct LogEvent {
    timestamp: chrono::DateTime<chrono::Utc>,
    level: String,
    target: String,
    message: String,
    fields: HashMap<String, serde_json::Value>,
}

pub struct StructuredLogger {
    sender: Arc<tokio::sync::mpsc::Sender<LogEvent>>,
}

impl StructuredLogger {
    pub fn new() -> (Self, LogReceiver) {
        let (tx, rx) = tokio::sync::mpsc::channel(1000);
        
        let logger = Self {
            sender: Arc::new(tx),
        };
        
        let receiver = LogReceiver { receiver: rx };
        
        (logger, receiver)
    }
}

impl Subscriber for StructuredLogger {
    fn enabled(&self, metadata: &tracing::Metadata<'_>) -> bool {
        metadata.level() <= &Level::INFO
    }

    fn new_span(&self, attrs: &Attributes<'_>) -> tracing::Id {
        // Implementation for span creation
        tracing::Id::from_u64(0)
    }

    fn record(&self, _span: &tracing::Id, _values: &Record<'_>) {
        // Implementation for recording span values
    }

    fn record_follows_from(
        &self,
        _span: &tracing::Id,
        _follows: &tracing::Id,
    ) {
        // Implementation for span relationships
    }

    fn event(&self, event: &Event<'_>) {
        let mut visitor = JsonVisitor::default();
        event.record(&mut visitor);
        
        let log_event = LogEvent {
            timestamp: chrono::Utc::now(),
            level: event.metadata().level().to_string(),
            target: event.metadata().target().to_string(),
            message: visitor.message,
            fields: visitor.fields,
        };
        
        let sender = self.sender.clone();
        tokio::spawn(async move {
            if let Err(e) = sender.send(log_event).await {
                eprintln!("Failed to send log event: {}", e);
            }
        });
    }
}

#[derive(Default)]
struct JsonVisitor {
    message: String,
    fields: HashMap<String, serde_json::Value>,
}

impl Visit for JsonVisitor {
    fn record_debug(
        &mut self,
        field: &Field,
        value: &dyn std::fmt::Debug,
    ) {
        self.fields.insert(
            field.name().to_string(),
            serde_json::Value::String(format!("{:?}", value)),
        );
    }

    fn record_str(&mut self, field: &Field, value: &str) {
        if field.name() == "message" {
            self.message = value.to_string();
        } else {
            self.fields.insert(
                field.name().to_string(),
                serde_json::Value::String(value.to_string()),
            );
        }
    }

    fn record_i64(&mut self, field: &Field, value: i64) {
        self.fields.insert(
            field.name().to_string(),
            serde_json::Value::Number(value.into()),
        );
    }

    fn record_u64(&mut self, field: &Field, value: u64) {
        self.fields.insert(
            field.name().to_string(),
            serde_json::Value::Number(value.into()),
        );
    }

    fn record_bool(&mut self, field: &Field, value: bool) {
        self.fields.insert(
            field.name().to_string(),
            serde_json::Value::Bool(value),
        );
    }
}
```

---

## Analytics Collection

Implement analytics collection:

```rust
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use serde::Serialize;
use time::OffsetDateTime;

#[derive(Debug, Clone, Serialize)]
pub struct RequestAnalytics {
    timestamp: OffsetDateTime,
    method: String,
    path: String,
    status: u16,
    duration_ms: u64,
    client_ip: String,
    user_agent: String,
    referer: Option<String>,
    backend: String,
}

#[derive(Debug, Clone, Serialize)]
pub struct AggregatedAnalytics {
    period_start: OffsetDateTime,
    period_end: OffsetDateTime,
    total_requests: u64,
    status_codes: HashMap<u16, u64>,
    avg_duration_ms: f64,
    unique_clients: u64,
    paths: HashMap<String, u64>,
    backends: HashMap<String, BackendStats>,
}

#[derive(Debug, Clone, Serialize)]
pub struct BackendStats {
    requests: u64,
    errors: u64,
    avg_duration_ms: f64,
}

pub struct AnalyticsCollector {
    storage: Arc<dyn AnalyticsStorage>,
    current_period: RwLock<Vec<RequestAnalytics>>,
    aggregation_period: time::Duration,
}

impl AnalyticsCollector {
    pub fn new(
        storage: Arc<dyn AnalyticsStorage>,
        aggregation_period: time::Duration,
    ) -> Self {
        Self {
            storage,
            current_period: RwLock::new(Vec::new()),
            aggregation_period,
        }
    }

    pub async fn record_request(
        &self,
        analytics: RequestAnalytics,
    ) {
        let mut current = self.current_period.write().await;
        current.push(analytics);
    }

    pub async fn start_aggregation(self: Arc<Self>) {
        let mut interval = tokio::time::interval(
            std::time::Duration::from_secs(60),
        );
        
        loop {
            interval.tick().await;
            self.aggregate_and_store().await;
        }
    }

    async fn aggregate_and_store(&self) {
        // Swap current period with empty vec
        let current = {
            let mut current = self.current_period.write().await;
            std::mem::take(&mut *current)
        };
        
        if current.is_empty() {
            return;
        }
        
        // Calculate period boundaries
        let now = OffsetDateTime::now_utc();
        let period_start = now - self.aggregation_period;
        
        // Aggregate analytics
        let mut aggregated = AggregatedAnalytics {
            period_start,
            period_end: now,
            total_requests: current.len() as u64,
            status_codes: HashMap::new(),
            avg_duration_ms: 0.0,
            unique_clients: 0,
            paths: HashMap::new(),
            backends: HashMap::new(),
        };
        
        let mut total_duration = 0u64;
        let mut unique_ips = std::collections::HashSet::new();
        
        for analytics in &current {
            // Status codes
            *aggregated
                .status_codes
                .entry(analytics.status)
                .or_default() += 1;
                
            // Duration
            total_duration += analytics.duration_ms;
            
            // Unique clients
            unique_ips.insert(analytics.client_ip.clone());
            
            // Paths
            *aggregated
                .paths
                .entry(analytics.path.clone())
                .or_default() += 1;
                
            // Backend stats
            let backend_stats = aggregated
                .backends
                .entry(analytics.backend.clone())
                .or_insert_with(|| BackendStats {
                    requests: 0,
                    errors: 0,
                    avg_duration_ms: 0.0,
                });
                
            backend_stats.requests += 1;
            if analytics.status >= 500 {
                backend_stats.errors += 1;
            }
        }
        
        // Calculate averages
        aggregated.avg_duration_ms =
            total_duration as f64 / current.len() as f64;
        aggregated.unique_clients = unique_ips.len() as u64;
        
        for stats in aggregated.backends.values_mut() {
            stats.avg_duration_ms = total_duration as f64
                / stats.requests as f64;
        }
        
        // Store aggregated data
        if let Err(e) = self
            .storage
            .store_aggregated(aggregated)
            .await
        {
            error!("Failed to store analytics: {}", e);
        }
    }
}

#[async_trait]
pub trait AnalyticsStorage: Send + Sync {
    async fn store_request(
        &self,
        analytics: RequestAnalytics,
    ) -> Result<(), Box<dyn std::error::Error>>;
    
    async fn store_aggregated(
        &self,
        analytics: AggregatedAnalytics,
    ) -> Result<(), Box<dyn std::error::Error>>;
    
    async fn query_analytics(
        &self,
        start: OffsetDateTime,
        end: OffsetDateTime,
    ) -> Result<Vec<AggregatedAnalytics>, Box<dyn std::error::Error>>;
}
```

---

## Log Aggregation

Implement log aggregation:

```rust
use elasticsearch::{
    Elasticsearch, Error,
    http::transport::Transport,
    IndexParts,
};
use serde_json::json;

pub struct LogAggregator {
    client: Elasticsearch,
    index_prefix: String,
}

impl LogAggregator {
    pub fn new(
        hosts: &[String],
        index_prefix: &str,
    ) -> Result<Self, Error> {
        let transport = Transport::new(
            Transport::SingleNode {
                url: hosts[0].as_str().into(),
            },
        )?;
        
        let client = Elasticsearch::new(transport);
        
        Ok(Self {
            client,
            index_prefix: index_prefix.to_string(),
        })
    }

    pub async fn store_logs(
        &self,
        logs: Vec<LogEvent>,
    ) -> Result<(), Error> {
        if logs.is_empty() {
            return Ok(());
        }
        
        let index = format!(
            "{}-{}",
            self.index_prefix,
            chrono::Utc::now().format("%Y.%m.%d"),
        );
        
        let mut body = Vec::with_capacity(logs.len() * 2);
        
        for log in logs {
            // Add action line
            body.push(json!({
                "index": {
                    "_index": index,
                }
            }));
            
            // Add document
            body.push(json!({
                "timestamp": log.timestamp,
                "level": log.level,
                "target": log.target,
                "message": log.message,
                "fields": log.fields,
            }));
        }
        
        self.client
            .bulk(elasticsearch::BulkParts::None)
            .body(body)
            .send()
            .await?;
            
        Ok(())
    }

    pub async fn query_logs(
        &self,
        query: LogQuery,
    ) -> Result<Vec<LogEvent>, Error> {
        let index_pattern = format!("{}*", self.index_prefix);
        
        let mut search_query = json!({
            "query": {
                "bool": {
                    "must": []
                }
            },
            "sort": [
                { "timestamp": { "order": "desc" } }
            ],
            "size": query.limit,
        });
        
        let must = search_query["query"]["bool"]["must"]
            .as_array_mut()
            .unwrap();
            
        // Add time range
        must.push(json!({
            "range": {
                "timestamp": {
                    "gte": query.start.to_rfc3339(),
                    "lte": query.end.to_rfc3339(),
                }
            }
        }));
        
        // Add level filter
        if let Some(level) = query.level {
            must.push(json!({
                "term": {
                    "level": level.to_string()
                }
            }));
        }
        
        // Add message search
        if let Some(message) = query.message {
            must.push(json!({
                "match": {
                    "message": message
                }
            }));
        }
        
        let response = self
            .client
            .search(elasticsearch::SearchParts::Index(&[&index_pattern]))
            .body(search_query)
            .send()
            .await?;
            
        let response_body = response.json::<serde_json::Value>().await?;
        
        let hits = response_body["hits"]["hits"]
            .as_array()
            .unwrap();
            
        let logs = hits
            .iter()
            .map(|hit| {
                let source = &hit["_source"];
                serde_json::from_value(source.clone()).unwrap()
            })
            .collect();
            
        Ok(logs)
    }
}

pub struct LogQuery {
    pub start: chrono::DateTime<chrono::Utc>,
    pub end: chrono::DateTime<chrono::Utc>,
    pub level: Option<Level>,
    pub message: Option<String>,
    pub limit: u64,
}
```

---

## Metrics Dashboard

Implement metrics dashboard:

```rust
use warp::Filter;
use handlebars::Handlebars;
use serde_json::json;

pub struct MetricsDashboard {
    analytics: Arc<AnalyticsCollector>,
    log_aggregator: Arc<LogAggregator>,
    template_engine: Handlebars<'static>,
}

impl MetricsDashboard {
    pub fn new(
        analytics: Arc<AnalyticsCollector>,
        log_aggregator: Arc<LogAggregator>,
    ) -> Self {
        let mut handlebars = Handlebars::new();
        handlebars
            .register_template_string("dashboard", DASHBOARD_TEMPLATE)
            .unwrap();
            
        Self {
            analytics,
            log_aggregator,
            template_engine: handlebars,
        }
    }

    pub async fn start(self) {
        let dashboard = warp::path::end()
            .and(with_dashboard(Arc::new(self)))
            .and_then(render_dashboard);
            
        let api = warp::path("api")
            .and(
                warp::path("analytics")
                    .and(with_dashboard(Arc::new(self)))
                    .and_then(get_analytics)
                    .or(warp::path("logs")
                        .and(with_dashboard(Arc::new(self)))
                        .and_then(get_logs)),
            );
            
        let routes = dashboard
            .or(api)
            .with(warp::cors().allow_any_origin());
            
        warp::serve(routes)
            .run(([0, 0, 0, 0], 3000))
            .await;
    }

    async fn render_dashboard(
        &self,
    ) -> Result<impl warp::Reply, warp::Rejection> {
        let analytics = self
            .analytics
            .query_recent(chrono::Duration::hours(24))
            .await?;
            
        let logs = self
            .log_aggregator
            .query_logs(LogQuery {
                start: chrono::Utc::now()
                    - chrono::Duration::hours(24),
                end: chrono::Utc::now(),
                level: Some(Level::ERROR),
                message: None,
                limit: 100,
            })
            .await?;
            
        let data = json!({
            "analytics": analytics,
            "logs": logs,
        });
        
        let html = self
            .template_engine
            .render("dashboard", &data)
            .map_err(|e| {
                error!("Template error: {}", e);
                warp::reject::custom(DashboardError)
            })?;
            
        Ok(warp::reply::html(html))
    }
}

const DASHBOARD_TEMPLATE: &str = r#"
<!DOCTYPE html>
<html>
<head>
    <title>Load Balancer Dashboard</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@2.9.4/dist/Chart.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@2.9.4/dist/Chart.min.js"></script>
</head>
<body>
    <div class="container">
        <h1>Load Balancer Dashboard</h1>
        
        <!-- Request Rate Chart -->
        <div class="chart-container">
            <canvas id="requestChart"></canvas>
        </div>
        
        <!-- Status Codes Chart -->
        <div class="chart-container">
            <canvas id="statusChart"></canvas>
        </div>
        
        <!-- Backend Performance -->
        <div class="chart-container">
            <canvas id="backendChart"></canvas>
        </div>
        
        <!-- Recent Errors -->
        <div class="logs-container">
            <h2>Recent Errors</h2>
            <table>
                <thead>
                    <tr>
                        <th>Time</th>
                        <th>Message</th>
                    </tr>
                </thead>
                <tbody>
                    {{#each logs}}
                    <tr>
                        <td>{{this.timestamp}}</td>
                        <td>{{this.message}}</td>
                    </tr>
                    {{/each}}
                </tbody>
            </table>
        </div>
    </div>
    
    <script>
        // Initialize charts with data
        const requestData = {
            labels: {{analytics.timestamps}},
            datasets: [{
                label: 'Requests per minute',
                data: {{analytics.request_rates}},
            }]
        };
        
        new Chart('requestChart', {
            type: 'line',
            data: requestData,
        });
        
        // Add more chart initializations
    </script>
</body>
</html>
"#;
```

---

## Summary
- Implemented structured logging
- Added analytics collection
- Created log aggregation
- Implemented metrics dashboard
- Added reporting features

---

## Next Steps
Continue to [Module 25: Performance Optimization](25-performance-optimization.md) to learn about optimizing load balancer performance.
