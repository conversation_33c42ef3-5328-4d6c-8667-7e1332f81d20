# Module 06: Configuration System

## Learning Objectives
- Design flexible configuration systems using serde and multiple formats
- Implement configuration validation and error handling
- <PERSON>rn about environment variable integration and precedence
- Build hot-reload capabilities for production systems
- Understand configuration security and secret management
- Create CLI argument parsing with clap

## Prerequisites
- Completion of Module 05: Health Checking System
- Understanding of serialization/deserialization concepts
- Familiarity with YAML, TOML, and JSON formats
- Knowledge of environment variables and command-line arguments

## Navigation
- [Previous: Health Checking System](05-health-checking.md)
- [Next: Advanced Logging and Observability](07-logging-observability.md)

## Configuration System Design

A robust configuration system is essential for production load balancers. It must support multiple sources, validation, and runtime updates.

### Configuration Sources Priority

```mermaid
flowchart TD
    CLI[Command Line Arguments] --> ENV[Environment Variables]
    ENV --> FILE[Configuration Files]
    FILE --> DEFAULT[Default Values]
    
    CLI -.-> |Highest Priority| FINAL[Final Configuration]
    ENV -.-> FINAL
    FILE -.-> FINAL
    DEFAULT -.-> |Lowest Priority| FINAL
```

### Configuration Categories

**Server Configuration**:
- Bind address and port
- TLS/SSL settings
- Connection limits

**Backend Configuration**:
- Server addresses and weights
- Health check settings
- Load balancing algorithms

**Operational Configuration**:
- Logging levels and formats
- Metrics collection
- Administrative interfaces

## Implementation: Configuration System

Let's build a comprehensive configuration system.

### Configuration Structure

Create `src/config/mod.rs`:

```rust
//! Configuration management system
//! 
//! This module handles loading, parsing, and validating configuration
//! from multiple sources including files, environment variables, and CLI args.

pub mod settings;
pub mod loader;
pub mod validation;

pub use settings::{Config, ServerConfig, BackendConfig, HealthCheckConfig, LoggingConfig};
pub use loader::ConfigLoader;

use serde::{Deserialize, Serialize};
use std::net::SocketAddr;
use std::path::PathBuf;
use std::time::Duration;

/// Main configuration structure
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Config {
    /// Server configuration
    pub server: ServerConfig,
    /// Backend servers configuration
    pub backends: Vec<BackendConfig>,
    /// Health checking configuration
    pub health_check: HealthCheckConfig,
    /// Logging configuration
    pub logging: LoggingConfig,
    /// Load balancing algorithm
    pub load_balancing: LoadBalancingConfig,
}

/// Server configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerConfig {
    /// Address to bind to
    pub bind_address: SocketAddr,
    /// Maximum concurrent connections
    pub max_connections: usize,
    /// Connection timeout
    pub connection_timeout: Duration,
    /// Enable TLS
    pub tls_enabled: bool,
    /// TLS certificate path
    pub tls_cert_path: Option<PathBuf>,
    /// TLS private key path
    pub tls_key_path: Option<PathBuf>,
}

/// Backend server configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BackendConfig {
    /// Backend server address
    pub address: SocketAddr,
    /// Weight for weighted load balancing (1-100)
    pub weight: u32,
    /// Maximum connections to this backend
    pub max_connections: usize,
    /// Connection timeout for this backend
    pub connection_timeout: Duration,
    /// Backend-specific health check override
    pub health_check: Option<HealthCheckConfig>,
}

/// Health check configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthCheckConfig {
    /// Enable health checking
    pub enabled: bool,
    /// Health check interval
    pub interval: Duration,
    /// Health check timeout
    pub timeout: Duration,
    /// Health check path (for HTTP checks)
    pub path: String,
    /// Expected HTTP status codes
    pub expected_status: Vec<u16>,
    /// Failure threshold before marking unhealthy
    pub failure_threshold: u32,
    /// Success threshold before marking healthy
    pub success_threshold: u32,
}

/// Logging configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoggingConfig {
    /// Log level (error, warn, info, debug, trace)
    pub level: String,
    /// Log format (json, pretty, compact)
    pub format: String,
    /// Log to file
    pub file_path: Option<PathBuf>,
    /// Enable access logging
    pub access_log: bool,
    /// Access log format
    pub access_log_format: String,
}

/// Load balancing configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoadBalancingConfig {
    /// Algorithm (round_robin, weighted_round_robin, least_connections)
    pub algorithm: String,
    /// Session affinity settings
    pub session_affinity: Option<SessionAffinityConfig>,
}

/// Session affinity configuration
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SessionAffinityConfig {
    /// Enable session affinity
    pub enabled: bool,
    /// Cookie name for session affinity
    pub cookie_name: String,
    /// Session timeout
    pub timeout: Duration,
}

impl Default for Config {
    fn default() -> Self {
        Self {
            server: ServerConfig::default(),
            backends: vec![],
            health_check: HealthCheckConfig::default(),
            logging: LoggingConfig::default(),
            load_balancing: LoadBalancingConfig::default(),
        }
    }
}

impl Default for ServerConfig {
    fn default() -> Self {
        Self {
            bind_address: "127.0.0.1:8080".parse().unwrap(),
            max_connections: 10000,
            connection_timeout: Duration::from_secs(30),
            tls_enabled: false,
            tls_cert_path: None,
            tls_key_path: None,
        }
    }
}

impl Default for HealthCheckConfig {
    fn default() -> Self {
        Self {
            enabled: true,
            interval: Duration::from_secs(30),
            timeout: Duration::from_secs(5),
            path: "/health".to_string(),
            expected_status: vec![200],
            failure_threshold: 3,
            success_threshold: 2,
        }
    }
}

impl Default for LoggingConfig {
    fn default() -> Self {
        Self {
            level: "info".to_string(),
            format: "pretty".to_string(),
            file_path: None,
            access_log: true,
            access_log_format: "combined".to_string(),
        }
    }
}

impl Default for LoadBalancingConfig {
    fn default() -> Self {
        Self {
            algorithm: "round_robin".to_string(),
            session_affinity: None,
        }
    }
}
```

### Configuration Loader

Create `src/config/loader.rs`:

```rust
use super::Config;
use anyhow::{Context, Result};
use clap::{Arg, Command};
use serde::de::DeserializeOwned;
use std::env;
use std::fs;
use std::path::Path;
use tracing::{info, debug, warn};

/// Configuration loader that handles multiple sources
pub struct ConfigLoader {
    config_path: Option<String>,
    env_prefix: String,
}

impl ConfigLoader {
    /// Create a new configuration loader
    pub fn new() -> Self {
        Self {
            config_path: None,
            env_prefix: "RUSTY_LB".to_string(),
        }
    }
    
    /// Set configuration file path
    pub fn with_config_path<P: AsRef<Path>>(mut self, path: P) -> Self {
        self.config_path = Some(path.as_ref().to_string_lossy().to_string());
        self
    }
    
    /// Set environment variable prefix
    pub fn with_env_prefix(mut self, prefix: String) -> Self {
        self.env_prefix = prefix;
        self
    }
    
    /// Load configuration from all sources
    pub fn load(&self) -> Result<Config> {
        info!("Loading configuration...");
        
        // Start with default configuration
        let mut config = Config::default();
        
        // Load from configuration file if specified
        if let Some(config_path) = &self.config_path {
            config = self.load_from_file(config_path)
                .with_context(|| format!("Failed to load config from {}", config_path))?;
            info!("Loaded configuration from file: {}", config_path);
        }
        
        // Override with environment variables
        config = self.apply_env_overrides(config)?;
        
        // Override with command line arguments
        config = self.apply_cli_overrides(config)?;
        
        // Validate configuration
        self.validate_config(&config)?;
        
        info!("Configuration loaded successfully");
        debug!("Final configuration: {:#?}", config);
        
        Ok(config)
    }
    
    /// Load configuration from file
    fn load_from_file<P: AsRef<Path>>(&self, path: P) -> Result<Config> {
        let path = path.as_ref();
        let content = fs::read_to_string(path)
            .with_context(|| format!("Failed to read config file: {}", path.display()))?;
        
        let config = match path.extension().and_then(|s| s.to_str()) {
            Some("yaml") | Some("yml") => {
                serde_yaml::from_str(&content)
                    .with_context(|| "Failed to parse YAML config")?
            }
            Some("toml") => {
                toml::from_str(&content)
                    .with_context(|| "Failed to parse TOML config")?
            }
            Some("json") => {
                serde_json::from_str(&content)
                    .with_context(|| "Failed to parse JSON config")?
            }
            _ => {
                return Err(anyhow::anyhow!(
                    "Unsupported config file format. Use .yaml, .toml, or .json"
                ));
            }
        };
        
        Ok(config)
    }
    
    /// Apply environment variable overrides
    fn apply_env_overrides(&self, mut config: Config) -> Result<Config> {
        debug!("Applying environment variable overrides with prefix: {}", self.env_prefix);
        
        // Server configuration overrides
        if let Ok(bind_addr) = env::var(format!("{}_BIND_ADDRESS", self.env_prefix)) {
            config.server.bind_address = bind_addr.parse()
                .with_context(|| "Invalid bind address in environment variable")?;
            debug!("Override bind_address from env: {}", config.server.bind_address);
        }
        
        if let Ok(max_conn) = env::var(format!("{}_MAX_CONNECTIONS", self.env_prefix)) {
            config.server.max_connections = max_conn.parse()
                .with_context(|| "Invalid max_connections in environment variable")?;
            debug!("Override max_connections from env: {}", config.server.max_connections);
        }
        
        // Health check overrides
        if let Ok(enabled) = env::var(format!("{}_HEALTH_CHECK_ENABLED", self.env_prefix)) {
            config.health_check.enabled = enabled.parse()
                .with_context(|| "Invalid health_check.enabled in environment variable")?;
            debug!("Override health_check.enabled from env: {}", config.health_check.enabled);
        }
        
        if let Ok(interval) = env::var(format!("{}_HEALTH_CHECK_INTERVAL", self.env_prefix)) {
            let seconds: u64 = interval.parse()
                .with_context(|| "Invalid health_check.interval in environment variable")?;
            config.health_check.interval = std::time::Duration::from_secs(seconds);
            debug!("Override health_check.interval from env: {:?}", config.health_check.interval);
        }
        
        // Logging overrides
        if let Ok(level) = env::var(format!("{}_LOG_LEVEL", self.env_prefix)) {
            config.logging.level = level;
            debug!("Override logging.level from env: {}", config.logging.level);
        }
        
        if let Ok(format) = env::var(format!("{}_LOG_FORMAT", self.env_prefix)) {
            config.logging.format = format;
            debug!("Override logging.format from env: {}", config.logging.format);
        }
        
        Ok(config)
    }
    
    /// Apply command line argument overrides
    fn apply_cli_overrides(&self, mut config: Config) -> Result<Config> {
        let matches = Command::new("rusty-balancer")
            .version("1.0")
            .about("High-performance load balancer written in Rust")
            .arg(Arg::new("config")
                .short('c')
                .long("config")
                .value_name("FILE")
                .help("Configuration file path"))
            .arg(Arg::new("bind-address")
                .short('b')
                .long("bind-address")
                .value_name("ADDRESS")
                .help("Address to bind to (e.g., 127.0.0.1:8080)"))
            .arg(Arg::new("log-level")
                .short('l')
                .long("log-level")
                .value_name("LEVEL")
                .help("Log level (error, warn, info, debug, trace)"))
            .arg(Arg::new("health-check-disabled")
                .long("no-health-check")
                .action(clap::ArgAction::SetTrue)
                .help("Disable health checking"))
            .get_matches();
        
        // Override bind address
        if let Some(bind_addr) = matches.get_one::<String>("bind-address") {
            config.server.bind_address = bind_addr.parse()
                .with_context(|| "Invalid bind address from command line")?;
            debug!("Override bind_address from CLI: {}", config.server.bind_address);
        }
        
        // Override log level
        if let Some(log_level) = matches.get_one::<String>("log-level") {
            config.logging.level = log_level.clone();
            debug!("Override logging.level from CLI: {}", config.logging.level);
        }
        
        // Override health check enabled
        if matches.get_flag("health-check-disabled") {
            config.health_check.enabled = false;
            debug!("Disabled health checking from CLI");
        }
        
        Ok(config)
    }
    
    /// Validate configuration
    fn validate_config(&self, config: &Config) -> Result<()> {
        // Validate server configuration
        if config.server.max_connections == 0 {
            return Err(anyhow::anyhow!("max_connections must be greater than 0"));
        }
        
        if config.server.connection_timeout.as_secs() == 0 {
            return Err(anyhow::anyhow!("connection_timeout must be greater than 0"));
        }
        
        // Validate backends
        if config.backends.is_empty() {
            warn!("No backend servers configured");
        }
        
        for (i, backend) in config.backends.iter().enumerate() {
            if backend.weight == 0 || backend.weight > 100 {
                return Err(anyhow::anyhow!(
                    "Backend {} weight must be between 1 and 100", i
                ));
            }
            
            if backend.max_connections == 0 {
                return Err(anyhow::anyhow!(
                    "Backend {} max_connections must be greater than 0", i
                ));
            }
        }
        
        // Validate health check configuration
        if config.health_check.enabled {
            if config.health_check.failure_threshold == 0 {
                return Err(anyhow::anyhow!("failure_threshold must be greater than 0"));
            }
            
            if config.health_check.success_threshold == 0 {
                return Err(anyhow::anyhow!("success_threshold must be greater than 0"));
            }
            
            if config.health_check.timeout >= config.health_check.interval {
                return Err(anyhow::anyhow!(
                    "health_check.timeout must be less than health_check.interval"
                ));
            }
        }
        
        // Validate logging configuration
        let valid_log_levels = ["error", "warn", "info", "debug", "trace"];
        if !valid_log_levels.contains(&config.logging.level.as_str()) {
            return Err(anyhow::anyhow!(
                "Invalid log level: {}. Must be one of: {:?}",
                config.logging.level, valid_log_levels
            ));
        }
        
        let valid_log_formats = ["json", "pretty", "compact"];
        if !valid_log_formats.contains(&config.logging.format.as_str()) {
            return Err(anyhow::anyhow!(
                "Invalid log format: {}. Must be one of: {:?}",
                config.logging.format, valid_log_formats
            ));
        }
        
        // Validate load balancing configuration
        let valid_algorithms = ["round_robin", "weighted_round_robin", "least_connections"];
        if !valid_algorithms.contains(&config.load_balancing.algorithm.as_str()) {
            return Err(anyhow::anyhow!(
                "Invalid load balancing algorithm: {}. Must be one of: {:?}",
                config.load_balancing.algorithm, valid_algorithms
            ));
        }
        
        Ok(())
    }
}

impl Default for ConfigLoader {
    fn default() -> Self {
        Self::new()
    }
}
```

### Example Configuration Files

Create example configuration files:

**config.yaml**:
```yaml
server:
  bind_address: "0.0.0.0:8080"
  max_connections: 10000
  connection_timeout: "30s"
  tls_enabled: false

backends:
  - address: "127.0.0.1:8081"
    weight: 1
    max_connections: 1000
    connection_timeout: "10s"
  - address: "127.0.0.1:8082"
    weight: 2
    max_connections: 1000
    connection_timeout: "10s"
  - address: "127.0.0.1:8083"
    weight: 1
    max_connections: 1000
    connection_timeout: "10s"

health_check:
  enabled: true
  interval: "30s"
  timeout: "5s"
  path: "/health"
  expected_status: [200, 204]
  failure_threshold: 3
  success_threshold: 2

logging:
  level: "info"
  format: "pretty"
  access_log: true
  access_log_format: "combined"

load_balancing:
  algorithm: "weighted_round_robin"
```

## Hot Configuration Reload

Implement configuration hot-reload for production systems:

```rust
use notify::{Watcher, RecursiveMode, watcher};
use std::sync::mpsc;
use std::time::Duration;

pub struct ConfigWatcher {
    config_path: PathBuf,
    reload_sender: mpsc::Sender<Config>,
}

impl ConfigWatcher {
    pub fn new(config_path: PathBuf) -> (Self, mpsc::Receiver<Config>) {
        let (tx, rx) = mpsc::channel();
        
        (Self {
            config_path,
            reload_sender: tx,
        }, rx)
    }
    
    pub fn start_watching(&self) -> Result<()> {
        let (tx, rx) = mpsc::channel();
        let mut watcher = watcher(tx, Duration::from_secs(1))?;
        
        watcher.watch(&self.config_path, RecursiveMode::NonRecursive)?;
        
        let config_path = self.config_path.clone();
        let reload_sender = self.reload_sender.clone();
        
        tokio::spawn(async move {
            loop {
                match rx.recv() {
                    Ok(_event) => {
                        match ConfigLoader::new().load_from_file(&config_path) {
                            Ok(new_config) => {
                                if let Err(e) = reload_sender.send(new_config) {
                                    error!("Failed to send config reload: {}", e);
                                    break;
                                }
                                info!("Configuration reloaded from file");
                            }
                            Err(e) => {
                                error!("Failed to reload configuration: {}", e);
                            }
                        }
                    }
                    Err(e) => {
                        error!("Config watcher error: {}", e);
                        break;
                    }
                }
            }
        });
        
        Ok(())
    }
}
```

## Integration with Main Application

Update the main application to use the configuration system:

```rust
// Update src/main.rs
use rusty_balancer::config::{ConfigLoader, Config};
use tracing::info;

#[tokio::main]
async fn main() -> rusty_balancer::Result<()> {
    // Load configuration first
    let config = ConfigLoader::new()
        .with_config_path("config.yaml")
        .load()?;
    
    // Initialize logging based on configuration
    init_logging(&config.logging)?;
    
    info!("Starting Rusty Load Balancer");
    info!("Bind address: {}", config.server.bind_address);
    info!("Backend servers: {}", config.backends.len());
    info!("Health checking: {}", config.health_check.enabled);
    
    // Initialize components with configuration
    let backend_pool = Arc::new(BackendPool::new());
    
    // Add backends from configuration
    for backend_config in &config.backends {
        backend_pool.add_backend(backend_config.address)?;
    }
    
    // Start health checker if enabled
    if config.health_check.enabled {
        let health_checker = HealthChecker::new(
            config.health_check.clone(),
            Arc::clone(&backend_pool)
        );
        
        tokio::spawn(async move {
            health_checker.start().await;
        });
    }
    
    // Start proxy server
    let proxy = HttpProxy::new(backend_pool);
    proxy.start(config.server.bind_address).await?;
    
    Ok(())
}

fn init_logging(config: &LoggingConfig) -> Result<()> {
    // Initialize tracing based on configuration
    // Implementation details...
    Ok(())
}
```

## Next Steps Preview

In Module 07, we'll enhance logging and observability:
- Structured logging with tracing
- Metrics collection and exposition
- Request tracing and correlation IDs
- Performance monitoring and alerting

## Key Takeaways

- **Multiple Sources**: Support files, environment variables, and CLI arguments
- **Validation**: Comprehensive validation prevents runtime errors
- **Hot Reload**: Enable configuration updates without restarts
- **Security**: Handle sensitive configuration data appropriately
- **Flexibility**: Support multiple configuration formats and deployment scenarios

A robust configuration system is the foundation for operating load balancers in production environments.

## Navigation
- [Previous: Health Checking System](05-health-checking.md)
- [Next: Advanced Logging and Observability](07-logging-observability.md)
